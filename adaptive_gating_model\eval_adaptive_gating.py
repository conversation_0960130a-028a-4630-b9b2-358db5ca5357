import sys
import os
# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from adaptive_gating_model.st_delay_model_v2 import STDelayModelV2
from data import data_processing
from forecast.lorenz96 import lorenz96_config
from scipy.stats import pearsonr
import numpy as np
import pickle
import tensorflow as tf


def eval_model(model, data_generator, config, set_name):
    losses = []
    pccs = []
    idxs = []

    for i in range(len(data_generator)):
        batch_data = data_generator[i]
        
        if len(batch_data) == 2:
            x, y = batch_data
        else:
            x, y = batch_data[0], batch_data[-1]
        
        # 预测
        predictions = model.model.predict(x, verbose=0)
        predict_y = predictions[1] if len(predictions) > 1 else predictions[0]

        # 计算RMSE
        loss = np.sqrt(np.mean((predict_y - y) ** 2))
        
        # 计算PCC
        pcc = pearsonr(predict_y.flatten(), y.flatten())[0]
        
        losses.append(loss)
        pccs.append(pcc)
        idxs.append(data_generator.idxs[i])
    
    # 统计结果
    mean_loss = np.mean(losses)
    mean_pcc = np.mean(pccs)
    
    print(f"{len(losses)}")
    print(f'mean loss: {mean_loss}')
    print(f'mean pcc: {mean_pcc}')
    
    return losses, pccs, idxs


if __name__ == '__main__':

    config = lorenz96_config.Lorenz96Config()

    print('loading lorenz  data...')
    data = data_processing.load_lorenz96_data(N=config.K, F=config.F, time_range=(0, 3000), dt=0.02)

    print(len(data))

    # 创建自适应门控模型
    model = STDelayModelV2(config, mode='prediction')

    train_idxs, val_idxs = data_processing.get_data_idxs_as_predict_idx_no_overlap(rate=0.9,
                                                                                   interval=60)

    mean_train_losses = []
    mean_train_pccs = []

    results = {}

    noised_data, y = data_processing.add_noise(data, config) 
    # 使用原始的数据生成器
    from forecast import st_delay_model
    train_generator = st_delay_model.DataGeneratorForLengthCmp(data, y, train_idxs, config)
    val_generator = None if val_idxs is None else st_delay_model.DataGeneratorForLengthCmp(data, y, val_idxs, config)

    # 指定自适应门控权重文件路径（使用最新训练的最佳模型）
    model_path = r"C:\Users\<USER>\Desktop\重新Delay-Embedding-based-Forecast-Machine-master\logs\2025_07_31-21_09_38(lorenz96_40_19_Yidx_0_adaptive_gating_model)\weights_epoch_0096_loss_0.046.h5"
    
    print(model_path)
    # 使用原始的load_weights方法
    model.load_weights(model_path)

    sets = {'train': train_generator, 'val': val_generator}

    for set_name, data_generator in sets.items():
        if data_generator is None:
            continue
        losses, pccs, idxs = eval_model(model, data_generator, config, set_name)
        results[set_name] = {'losses': losses, 'pccs': pccs, 'idxs': idxs}

    # 保存结果
    with open('adaptive_gating_eval_results.pkl', 'wb') as f:
        pickle.dump(results, f)

    print("自适应门控模型评估完成，结果已保存到 adaptive_gating_eval_results.pkl")
