#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Lorenz96空间相关性分析
验证多尺度卷积的合理性
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from data.data_processing import load_lorenz_data
from forecast.lorenz96.lorenz96_config import Lorenz96Config

def analyze_spatial_correlation():
    """分析Lorenz96的空间相关性模式"""
    
    # 加载数据
    config = Lorenz96Config()
    data = load_lorenz_data(config)
    
    # 取一段数据进行分析
    sample_data = data[:1000, :]  # 1000个时间步，60个空间点
    
    # 计算空间相关性矩阵
    correlation_matrix = np.corrcoef(sample_data.T)  # 60x60相关性矩阵
    
    # 分析不同距离的平均相关性
    N = sample_data.shape[1]  # 60
    distances = []
    correlations = []
    
    for d in range(1, N//2):  # 距离从1到30
        corr_values = []
        for i in range(N):
            j = (i + d) % N  # 考虑周期边界
            corr_values.append(correlation_matrix[i, j])
        
        distances.append(d)
        correlations.append(np.mean(corr_values))
    
    # 创建分析图
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. 相关性矩阵热图
    im1 = ax1.imshow(correlation_matrix, cmap='RdBu_r', vmin=-1, vmax=1)
    ax1.set_title('空间相关性矩阵', fontsize=14, fontweight='bold')
    ax1.set_xlabel('空间位置')
    ax1.set_ylabel('空间位置')
    plt.colorbar(im1, ax=ax1)
    
    # 2. 距离-相关性关系
    ax2.plot(distances, correlations, 'bo-', linewidth=2, markersize=6)
    ax2.set_title('空间距离 vs 平均相关性', fontsize=14, fontweight='bold')
    ax2.set_xlabel('空间距离')
    ax2.set_ylabel('平均相关性')
    ax2.grid(True, alpha=0.3)
    ax2.axhline(y=0, color='red', linestyle='--', alpha=0.5)
    
    # 标注关键距离点
    for i, (d, c) in enumerate(zip(distances[:10], correlations[:10])):
        ax2.annotate(f'{c:.3f}', (d, c), textcoords="offset points", 
                    xytext=(0,10), ha='center', fontsize=8)
    
    # 3. 多尺度相关性分析
    # 定义不同尺度的窗口
    scales = {
        '局部 (1-3)': list(range(1, 4)),
        '中尺度 (4-8)': list(range(4, 9)),
        '长程 (9-15)': list(range(9, 16))
    }
    
    scale_correlations = {}
    for scale_name, scale_range in scales.items():
        scale_corr = np.mean([correlations[d-1] for d in scale_range])
        scale_correlations[scale_name] = scale_corr
    
    scale_names = list(scale_correlations.keys())
    scale_values = list(scale_correlations.values())
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    
    bars = ax3.bar(scale_names, scale_values, color=colors, alpha=0.8, edgecolor='black')
    ax3.set_title('不同尺度的平均相关性', fontsize=14, fontweight='bold')
    ax3.set_ylabel('平均相关性')
    ax3.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, value in zip(bars, scale_values):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
    
    # 4. 卷积核大小建议
    ax4.axis('off')
    
    # 基于相关性分析给出卷积核建议
    analysis_text = [
        "空间相关性分析结果:",
        "",
        f"1. 最强相关性距离: {distances[np.argmax(correlations)]} (相关性: {max(correlations):.3f})",
        f"2. 局部相关性 (1-3): {scale_correlations['局部 (1-3)']:.3f}",
        f"3. 中尺度相关性 (4-8): {scale_correlations['中尺度 (4-8)']:.3f}",
        f"4. 长程相关性 (9-15): {scale_correlations['长程 (9-15)']:.3f}",
        "",
        "多尺度卷积核建议:",
        "",
        "• 局部分支: kernel_size=3 (捕捉最强相关性)",
        "• 中尺度分支: kernel_size=7 (捕捉中等距离相关性)",
        "• 全局分支: kernel_size=15 (捕捉长程相关性)",
        "",
        "合理性评估:",
        f"• 局部相关性显著: {'✅' if scale_correlations['局部 (1-3)'] > 0.5 else '❌'}",
        f"• 多尺度特征明显: {'✅' if len(set([round(v, 1) for v in scale_values])) > 1 else '❌'}",
        f"• 空间结构清晰: {'✅' if max(correlations) > 0.3 else '❌'}",
        "",
        "结论: " + ("✅ 多尺度卷积高度合理" if scale_correlations['局部 (1-3)'] > 0.3 else "❌ 空间相关性不明显")
    ]
    
    for i, line in enumerate(analysis_text):
        color = 'red' if line.startswith('结论:') else 'blue' if line.startswith('•') else 'black'
        weight = 'bold' if line.endswith(':') or line.startswith('结论:') else 'normal'
        ax4.text(0.05, 0.95 - i*0.04, line, transform=ax4.transAxes, 
                fontsize=10, fontweight=weight, color=color, verticalalignment='top')
    
    plt.tight_layout()
    plt.savefig('analysis/spatial_correlation_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return {
        'correlation_matrix': correlation_matrix,
        'distance_correlations': list(zip(distances, correlations)),
        'scale_correlations': scale_correlations,
        'max_correlation_distance': distances[np.argmax(correlations)],
        'max_correlation_value': max(correlations)
    }

if __name__ == "__main__":
    print("🔍 开始Lorenz96空间相关性分析...")
    
    results = analyze_spatial_correlation()
    
    print("\n📊 分析结果:")
    print(f"最强相关性距离: {results['max_correlation_distance']}")
    print(f"最强相关性值: {results['max_correlation_value']:.3f}")
    
    print("\n🎯 多尺度相关性:")
    for scale, corr in results['scale_correlations'].items():
        print(f"{scale}: {corr:.3f}")
    
    # 判断合理性
    local_corr = results['scale_correlations']['局部 (1-3)']
    if local_corr > 0.5:
        print("\n✅ 结论: 多尺度空间卷积高度合理!")
        print("   - 局部相关性强，适合小卷积核")
        print("   - 存在多尺度特征，适合多分支设计")
    elif local_corr > 0.3:
        print("\n✅ 结论: 多尺度空间卷积合理")
        print("   - 有一定空间相关性，卷积优于全连接")
    else:
        print("\n⚠️ 结论: 空间相关性较弱")
        print("   - 可能简单卷积就足够")
    
    print("\n✅ 分析完成！图表已保存到 analysis/spatial_correlation_analysis.png")
