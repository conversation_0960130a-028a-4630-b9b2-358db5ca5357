import sys
import os
# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import sys
import os
# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tcn_model.st_delay_model_v2 import STDelayModelV2
from data import data_processing
from forecast.lorenz96 import lorenz96_config
import tensorflow as tf

if __name__ == '__main__':

    config = lorenz96_config.Lorenz96Config()
    # 启用TCN
    config.USE_TCN_TEMPORAL = True

    gpus = tf.config.experimental.list_physical_devices(device_type='GPU')
    for gpu in gpus:
        tf.config.experimental.set_memory_growth(gpu, True)

    data = data_processing.load_lorenz96_data(N=config.K, F=config.F, time_range=(0, 3000), dt=0.02)

    train_idxs, val_idxs = data_processing.get_data_idxs_as_predict_idx_no_overlap(rate=0.8, interval=60)

    print(len(train_idxs))

    data, y = data_processing.add_noise(data, config)

    # 使用原始的数据生成器
    from forecast import st_delay_model
    train_generator = st_delay_model.DataGeneratorForLengthCmp(data, y, train_idxs, config)
    val_generator = None if val_idxs is None else st_delay_model.DataGeneratorForLengthCmp(data, y, val_idxs, config)

    # 使用TCN模型
    model = STDelayModelV2(config, mode='training',
                          log_dir_suffix=config.name + '_tcn')
    model.compile()

    model.train(train_generator, val_generator)
