import tensorflow as tf
from tensorflow import keras as K
from tensorflow.keras import layers as KL

class CausalDilatedConv1D(KL.Layer):
    """因果膨胀卷积层"""
    def __init__(self, filters, kernel_size, dilation_rate, **kwargs):
        super().__init__(**kwargs)
        self.filters = filters
        self.kernel_size = kernel_size
        self.dilation_rate = dilation_rate
        
        self.conv = KL.Conv1D(
            filters=filters,
            kernel_size=kernel_size,
            dilation_rate=dilation_rate,
            padding='causal',
            activation=None,
            kernel_initializer='he_normal'
        )
        
    def call(self, inputs):
        return self.conv(inputs)
    
    def get_config(self):
        config = super().get_config()
        config.update({
            'filters': self.filters,
            'kernel_size': self.kernel_size,
            'dilation_rate': self.dilation_rate
        })
        return config

class TCNResidualBlock(KL.Layer):
    """TCN残差块"""
    def __init__(self, filters, kernel_size, dilation_rate, dropout_rate=0.1, **kwargs):
        super().__init__(**kwargs)
        self.filters = filters
        self.kernel_size = kernel_size
        self.dilation_rate = dilation_rate
        self.dropout_rate = dropout_rate
        
        # 主分支
        self.conv1 = CausalDilatedConv1D(filters, kernel_size, dilation_rate)
        self.norm1 = KL.LayerNormalization()
        self.activation1 = KL.ReLU()
        self.dropout1 = KL.Dropout(dropout_rate)
        
        self.conv2 = CausalDilatedConv1D(filters, kernel_size, dilation_rate)
        self.norm2 = KL.LayerNormalization()
        self.activation2 = KL.ReLU()
        self.dropout2 = KL.Dropout(dropout_rate)
        
        # 残差连接维度匹配
        self.residual_conv = None
        
    def build(self, input_shape):
        if input_shape[-1] != self.filters:
            self.residual_conv = KL.Conv1D(self.filters, 1, padding='same')
        super().build(input_shape)
        
    def call(self, inputs, training=None):
        # 主分支处理
        x = self.conv1(inputs)
        x = self.norm1(x)
        x = self.activation1(x)
        x = self.dropout1(x, training=training)
        
        x = self.conv2(x)
        x = self.norm2(x)
        x = self.activation2(x)
        x = self.dropout2(x, training=training)
        
        # 残差连接
        residual = self.residual_conv(inputs) if self.residual_conv else inputs
        return KL.Add()([x, residual])
    
    def get_config(self):
        config = super().get_config()
        config.update({
            'filters': self.filters,
            'kernel_size': self.kernel_size,
            'dilation_rate': self.dilation_rate,
            'dropout_rate': self.dropout_rate
        })
        return config

class TCNTemporalModule(KL.Layer):
    """TCN时间模块，替代Transformer"""
    def __init__(self, config, **kwargs):
        super().__init__(**kwargs)
        self.config = config
        
        # 膨胀率序列
        self.dilations = [1, 2, 4, 8]
        
        # TCN残差块序列
        self.tcn_blocks = []
        for i, dilation in enumerate(self.dilations):
            self.tcn_blocks.append(
                TCNResidualBlock(
                    filters=getattr(config, 'TCN_FILTERS', 64),
                    kernel_size=3,
                    dilation_rate=dilation,
                    dropout_rate=config.DROP_RATE,
                    name=f'tcn_block_{i+1}'
                )
            )
        
        # 输出投影层
        self.output_projection = KL.Dense(
            config.TEMPORAL_DIM,
            activation=config.ACITVATION,
            name='tcn_output_projection'
        )
        
    def call(self, inputs, training=None):
        # 逐层处理
        x = inputs
        for block in self.tcn_blocks:
            x = block(x, training=training)
        
        # 输出投影
        output = self.output_projection(x)
        return output
    
    def get_receptive_field(self):
        """计算总感受野"""
        receptive_field = 1
        for dilation in self.dilations:
            receptive_field += (3 - 1) * dilation
        return receptive_field
    
    def get_config(self):
        config = super().get_config()
        config.update({
            'dilations': self.dilations
        })
        return config
