"""
创建预测对比图的示例脚本
展示如何使用原始模型的绘图功能和新的多子图功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
import matplotlib.pyplot as plt
from utils.multi_subplot_visualization import draw_paper_style_comparison, create_comparison_figure_from_results

def create_sample_prediction_data():
    """创建示例预测数据"""
    np.random.seed(42)
    
    results = {}
    
    # 定义不同条件
    conditions = [
        ('time_invariant_m80', 0.3, 0.95),  # (条件名, 噪声水平, 基础相关性)
        ('time_invariant_m60', 0.4, 0.92),
        ('time_invariant_m40', 0.5, 0.88),
        ('time_varying_m80', 0.6, 0.85),
        ('time_varying_m60', 0.8, 0.80),
        ('time_varying_m40', 1.0, 0.75)
    ]
    
    for condition, noise_level, base_corr in conditions:
        # 生成时间序列数据
        train_len = 60
        predict_len = 40
        total_len = train_len + predict_len
        
        # 生成类似Lorenz系统的复杂时间序列
        t = np.linspace(0, 10, total_len)
        
        # 基础信号：多个频率成分的组合
        signal1 = 8 * np.sin(2 * np.pi * 0.5 * t) * np.exp(-t/15)
        signal2 = 5 * np.cos(2 * np.pi * 1.2 * t) * np.exp(-t/20)
        signal3 = 3 * np.sin(2 * np.pi * 2.0 * t) * np.exp(-t/25)
        
        # 添加非线性成分
        nonlinear = 2 * np.sin(signal1/5) * np.cos(signal2/3)
        
        true_signal = signal1 + signal2 + signal3 + nonlinear
        
        # 分割数据
        known_y = true_signal[:train_len]
        label_y = true_signal[train_len:]
        
        # 生成预测值（添加误差）
        predict_y = label_y + np.random.normal(0, noise_level, len(label_y))
        
        # 为了使相关性更接近目标值，调整预测值
        current_corr = np.corrcoef(predict_y, label_y)[0, 1]
        if not np.isnan(current_corr):
            # 线性调整以达到目标相关性
            alpha = base_corr / current_corr if current_corr != 0 else 1
            predict_y = alpha * predict_y + (1 - alpha) * label_y
        
        # 计算最终指标
        loss = np.sqrt(np.mean((predict_y - label_y)**2))
        pcc = np.corrcoef(predict_y, label_y)[0, 1]
        
        results[condition] = {
            'known_y': known_y,
            'label_y': label_y,
            'predict_y': predict_y,
            'loss': loss,
            'pcc': pcc if not np.isnan(pcc) else 0.0
        }
        
        print(f"{condition}: RMSE={loss:.3f}, PCC={pcc:.3f}")
    
    return results

def demonstrate_original_draw_pic():
    """演示原始的draw_pic函数用法"""
    print("=== 演示原始draw_pic函数 ===")
    
    # 生成单个预测示例
    np.random.seed(42)
    train_len = 60
    predict_len = 40
    
    # 生成数据
    t = np.linspace(0, 8, train_len + predict_len)
    true_signal = 10 * np.sin(t) * np.exp(-t/10) + 5 * np.cos(2*t)
    
    known_y = true_signal[:train_len]
    label_y = true_signal[train_len:]
    predict_y = label_y + np.random.normal(0, 0.5, len(label_y))
    
    loss = np.sqrt(np.mean((predict_y - label_y)**2))
    pcc = np.corrcoef(predict_y, label_y)[0, 1]
    
    # 使用原始风格绘图
    plt.figure(figsize=(10, 6))
    
    # 模仿原始draw_pic函数的绘图逻辑
    fontsize = 17
    plt.title(f"lorenz, PCC:{pcc:.2f}", fontdict={'family': 'Times New Roman', 'size': fontsize})
    plt.xlabel('Time', fontdict={'family': 'Times New Roman', 'size': fontsize})
    plt.ylabel('Value', fontdict={'family': 'Times New Roman', 'size': fontsize})
    plt.yticks(fontproperties='Times New Roman', size=fontsize)
    plt.xticks(fontproperties='Times New Roman', size=fontsize)
    
    # 绘制完整的真实值
    all_y = np.concatenate([known_y, label_y])
    x = np.arange(len(all_y))
    plt.plot(x, all_y, color='blue', marker='.')
    
    # 绘制预测值
    x_predict = np.arange(train_len, len(all_y))
    plt.scatter(x_predict, predict_y, color='none', edgecolors='red', marker='o',
                label=f'trained_loss:{loss:.2f},pcc:{pcc:.2f}',
                zorder=10, linewidths=1.2, s=40)
    
    plt.legend(bbox_to_anchor=(0., 1.02, 1., .102), loc=0, ncol=2, mode="expand", borderaxespad=0.)
    plt.tight_layout()
    plt.savefig('original_style_prediction.png', dpi=200)
    plt.close()
    
    print("原始风格预测图已保存: original_style_prediction.png")

def main():
    """主函数"""
    print("🎨 预测图绘制演示")
    print("=" * 50)
    
    # 1. 演示原始draw_pic函数
    demonstrate_original_draw_pic()
    
    print("\n" + "=" * 50)
    
    # 2. 创建多子图对比
    print("=== 创建多子图预测对比 ===")
    
    # 生成示例数据
    results = create_sample_prediction_data()
    
    # 创建论文风格的对比图
    draw_paper_style_comparison(results, 'paper_style_comparison.png')
    
    # 创建通用多子图对比
    create_comparison_figure_from_results(results, 'general_comparison.png')
    
    print("\n✅ 所有图像已生成完成！")
    print("生成的文件:")
    print("  - original_style_prediction.png (原始风格单图)")
    print("  - paper_style_comparison.png (论文风格2x3对比图)")
    print("  - general_comparison.png (通用多子图对比)")
    
    print("\n📊 图像说明:")
    print("  🔵 蓝色线: 真实值 (known + label)")
    print("  🔴 红色圆圈: 预测值")
    print("  🟢 绿色背景: 预测区域")
    print("  📈 指标: RMSE (均方根误差) 和 PCC (皮尔逊相关系数)")

if __name__ == '__main__':
    main()
