#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自适应门控模型训练脚本
替换简单concat为自适应交互门控
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import tensorflow as tf
from data import data_processing
from forecast.lorenz96 import lorenz96_config
from adaptive_gating_model.st_delay_model_v2 import STDelayModelV2
from forecast import st_delay_model


def train_adaptive_gating_model():
    """训练自适应门控版本的模型"""
    
    print("开始训练自适应门控版ST-Delay模型...")
    
    # 配置
    config = lorenz96_config.Lorenz96Config()
    
    # GPU配置
    gpus = tf.config.experimental.list_physical_devices(device_type='GPU')
    for gpu in gpus:
        tf.config.experimental.set_memory_growth(gpu, True)
    
    # 加载数据
    print("加载Lorenz96数据...")
    data = data_processing.load_lorenz96_data(N=config.K, F=config.F, time_range=(0, 3000), dt=0.02)

    # 数据分割 - 增加训练样本和验证样本
    train_idxs, val_idxs = data_processing.get_data_idxs_as_predict_idx_no_overlap(rate=0.9, interval=60)
    print(f"训练样本数: {len(train_idxs)}")
    print(f"验证样本数: {len(val_idxs) if val_idxs is not None else 0}")

    # 添加噪声
    data, y = data_processing.add_noise(data, config)

    # 创建数据生成器
    train_generator = st_delay_model.DataGeneratorForLengthCmp(data, y, train_idxs, config)
    val_generator = None if val_idxs is None else st_delay_model.DataGeneratorForLengthCmp(data, y, val_idxs, config)

    # 创建自适应门控模型
    print("创建自适应门控版模型...")
    model = STDelayModelV2(config, mode='training', log_dir_suffix='adaptive_gating_model')

    # 编译模型
    print("编译模型...")
    model.compile()

    # 显示模型信息
    print("\n模型结构:")
    model.model.summary()

    # 参数统计
    from adaptive_gating_model.adaptive_gating import count_adaptive_gating_parameters
    param_stats = count_adaptive_gating_parameters(config)

    print(f"\n自适应门控模块参数统计:")
    print(f"总参数量: {param_stats['total_params']:,}")
    print(f"复杂度级别: {param_stats['complexity_level']}")
    print(f"主要组件:")
    print(f"  - 深层特征变换: {param_stats['transform_params']:,}")
    print(f"  - 多层交互网络: {param_stats['interaction_params']:,}")
    print(f"  - 复杂门控网络: {param_stats['gate_params']:,}")
    print(f"  - 动态路由网络: {param_stats['routing_params']:,}")
    print(f"  - 投影层: {param_stats['projection_params']:,}")

    # 开始训练
    print("\n开始训练...")
    model.train(train_generator, val_generator)

    print("自适应门控模型训练完成！")
    print(f"模型保存在: {model.log_dir}")
    
    return model


if __name__ == '__main__':
    try:
        model = train_adaptive_gating_model()
        print("\n 训练成功完成！")
        
        # 显示训练结果
        print(f"\n训练结果:")
        print(f"模型保存路径: {model.log_dir}")
        print(f"日志文件: {model.log_dir}/training.log")

    except Exception as e:
        print(f"\n训练过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
