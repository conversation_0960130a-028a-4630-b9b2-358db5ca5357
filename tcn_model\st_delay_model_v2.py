from forecast.st_delay_model import STDelayModel
from forecast.st_delay_model import time_distributed_graph
from utils import layers
import tensorflow as tf
from tensorflow import keras as K
from tensorflow.keras import layers as KL
from tensorflow.keras import models as KM

class STDelayModelV2(STDelayModel):
    """DEFM模型改进版，支持模块选择"""
    def __init__(self, config, mode='training', log_dir_suffix=''):
        super().__init__(config, mode, log_dir_suffix)
        
    def build_model(self):
        input = KL.Input(shape=(self.config.TRAIN_LEN, self.config.K), name='input', dtype=tf.float32)
        
        if self.mode == 'training':
            gt_y_matrix = KL.Input(shape=(self.config.TRAIN_LEN, self.config.EMBEDDING_LEN), 
                                 name='gt_y_matrix', dtype=tf.float32)
            gt_y = KL.Input(shape=(self.config.EMBEDDING_LEN - 1,), name='gt_y', dtype=tf.float32)

        input_drop = KL.SpatialDropout1D(rate=self.config.DROP_RATE, name='input_drop')(input)

        if not self.config.MERGE_ONLY:
            # 空间模块保持原样
            spatial_features = time_distributed_graph(input_drop, self.config.SPATIAL_NODES,
                                                     last_activation=self.config.MODULE_LAST_ACITVATION,
                                                     last_bn=True, activation=self.config.ACITVATION,
                                                     name_prefix='spatial',
                                                     kernel_initialzer=self.config.KERNEL_INITIALIZER,
                                                     weight_decay=self.config.WEIGHT_DECAY, 
                                                     bn=self.config.BN)

            # 时间模块：根据配置选择
            if hasattr(self.config, 'USE_TCN_TEMPORAL') and self.config.USE_TCN_TEMPORAL:
                from tcn_model.tcn_temporal import TCNTemporalModule
                temporal_features = TCNTemporalModule(self.config, name='tcn_temporal')(input_drop)
                print("使用TCN时间模块")
            else:
                # 原始Transformer时间模块
                positioned_input = layers.PositionEncodingLayer(self.config.TEMPORAL_DIM,
                                                              self.config.TRAIN_LEN)(input_drop)
                temporal_features = positioned_input
                for _ in range(self.config.ENCODING_LAYER_NUMS):
                    temporal_features = layers.encoder_graph(self.config.TEMPORAL_DIM,
                                                           self.config.NUM_HEADS,
                                                           self.config.DIFF,
                                                           self.config.TRAINING,
                                                           rate=0,
                                                           x=temporal_features)
                print("使用Transformer时间模块")

            # 融合模块保持原样
            if self.config.MERGE_FUNC == 'add':
                merge_features = KL.Add(name='merge_features')([spatial_features, temporal_features])
            elif self.config.MERGE_FUNC == 'concat':
                merge_features = KL.Concatenate(name='merge_features')([spatial_features, temporal_features])
        else:
            merge_features = input_drop

        # 后续部分完全保持原样
        delay_output = time_distributed_graph(merge_features, self.config.MERGE_MAP_NODES, 
                                            last_activation=False, last_bn=False, 
                                            activation=self.config.ACITVATION,
                                            name_prefix='merge_delay',
                                            kernel_initialzer=self.config.KERNEL_INITIALIZER,
                                            weight_decay=self.config.WEIGHT_DECAY, bn=self.config.BN)

        predict_y = layers.Matrix2Y(self.config, name='predict_y')(delay_output)

        if self.mode == 'training':
            known_y_loss = layers.KnownYLoss(self.config, name='known_y_loss')([gt_y_matrix, delay_output])
            consistent_loss = layers.TimeConsistentLoss(self.config, name='consistent_loss')(delay_output)
            predict_y_loss = layers.PredictYLoss(name='predict_y_loss')([gt_y, predict_y])

            return KM.Model(inputs=[input, gt_y_matrix, gt_y], 
                          outputs=[delay_output, predict_y, known_y_loss, consistent_loss, predict_y_loss])
        else:
            return KM.Model(inputs=input, outputs=[delay_output, predict_y])

    def get_latest_weights(self):
        """获取最新的权重文件"""
        import glob
        import os

        if hasattr(self, 'log_dir') and os.path.exists(self.log_dir):
            weight_files = glob.glob(os.path.join(self.log_dir, 'weights_epoch_*.h5'))
            if weight_files:
                # 按修改时间排序，返回最新的
                latest_file = max(weight_files, key=os.path.getmtime)
                return latest_file
        return None

    def compile(self):
        """编译模型 - 继承自父类"""
        return super().compile()

    def train(self, train_generator, val_generator):
        """训练模型 - 继承自父类"""
        return super().train(train_generator, val_generator)

    def load_weights(self, model_path):
        """加载权重 - 继承自父类"""
        return super().load_weights(model_path)
