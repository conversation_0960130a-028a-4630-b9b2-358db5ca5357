"""
多子图预测对比可视化工具
用于绘制类似论文中的多子图预测对比图
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Rectangle

def draw_multi_prediction_comparison(prediction_data_list, titles, 
                                   figsize=(18, 12), save_path=None,
                                   fontsize=14, dpi=300):
    """
    绘制多子图预测对比图
    
    Args:
        prediction_data_list: 预测数据列表，每个元素包含 (known_y, label_y, predict_y, loss, pcc)
        titles: 子图标题列表，如 ['Time-invariant, m = 80', 'Time-varying, m = 80']
        figsize: 图像大小
        save_path: 保存路径
        fontsize: 字体大小
        dpi: 图像分辨率
    """
    
    # 设置matplotlib参数
    plt.rcParams['figure.figsize'] = figsize
    plt.rcParams['savefig.dpi'] = dpi
    plt.rcParams['font.family'] = 'Times New Roman'
    
    # 计算子图布局
    n_plots = len(prediction_data_list)
    if n_plots <= 3:
        rows, cols = 1, n_plots
    else:
        rows, cols = 2, 3
    
    # 创建子图
    fig, axes = plt.subplots(rows, cols, figsize=figsize)
    if n_plots == 1:
        axes = [axes]
    elif rows == 1:
        axes = axes
    else:
        axes = axes.flatten()
    
    # 绘制每个子图
    for idx, (data, title) in enumerate(zip(prediction_data_list, titles)):
        if idx >= len(axes):
            break
            
        ax = axes[idx]
        known_y, label_y, predict_y, loss, pcc = data
        
        # 绘制单个预测图
        _draw_single_prediction(ax, known_y, label_y, predict_y, loss, pcc, 
                               title, fontsize, idx)
    
    # 隐藏多余的子图
    for idx in range(len(prediction_data_list), len(axes)):
        axes[idx].set_visible(False)
    
    # 调整布局
    plt.tight_layout(pad=2.0)
    
    # 保存或显示
    if save_path:
        plt.savefig(save_path, dpi=dpi, bbox_inches='tight')
        print(f"图像已保存到: {save_path}")
    else:
        plt.show()
    
    plt.close()

def _draw_single_prediction(ax, known_y, label_y, predict_y, loss, pcc, 
                           title, fontsize, subplot_idx):
    """绘制单个预测子图"""
    
    # 数据准备
    train_len = len(known_y)
    all_y = np.concatenate([known_y, label_y])
    x_all = np.arange(len(all_y))
    x_predict = np.arange(train_len, len(all_y))
    
    # 绘制真实值（蓝色线）
    ax.plot(x_all, all_y, color='blue', linewidth=2, label='True values')
    
    # 绘制预测值（红色圆圈）
    ax.scatter(x_predict, predict_y, color='none', edgecolors='red', 
               marker='o', s=40, linewidths=1.5, zorder=10, label='Predictions')
    
    # 添加预测区域的背景色
    prediction_start = train_len
    prediction_end = len(all_y)
    ax.axvspan(prediction_start, prediction_end, alpha=0.2, color='lightgreen', 
               label='Prediction region')
    
    # 设置标题和标签
    ax.set_title(f'{title}', fontsize=fontsize, fontweight='bold', pad=10)
    ax.set_xlabel('Time', fontsize=fontsize)
    ax.set_ylabel('$Z_k$', fontsize=fontsize)
    
    # 设置刻度
    ax.tick_params(axis='both', which='major', labelsize=fontsize-2)
    
    # 添加子图标签 (a), (b), (c)...
    subplot_labels = ['(a)', '(b)', '(c)', '(d)', '(e)', '(f)']
    if subplot_idx < len(subplot_labels):
        ax.text(0.02, 0.98, subplot_labels[subplot_idx], 
                transform=ax.transAxes, fontsize=fontsize+2, fontweight='bold',
                verticalalignment='top', bbox=dict(boxstyle='round', 
                facecolor='white', alpha=0.8))
    
    # 添加性能指标文本
    metrics_text = f'RMSE: {loss:.3f}\nPCC: {pcc:.3f}'
    ax.text(0.98, 0.02, metrics_text, transform=ax.transAxes, 
            fontsize=fontsize-2, verticalalignment='bottom', 
            horizontalalignment='right',
            bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8))
    
    # 网格
    ax.grid(True, alpha=0.3, linestyle='--')
    
    # 图例（只在第一个子图显示）
    if subplot_idx == 0:
        ax.legend(loc='upper left', fontsize=fontsize-2, framealpha=0.9)

def create_comparison_figure_from_results(results_dict, save_path=None):
    """
    从结果字典创建对比图
    
    Args:
        results_dict: 结果字典，格式如下:
        {
            'time_invariant_m80': {
                'known_y': [...],
                'label_y': [...], 
                'predict_y': [...],
                'loss': 0.123,
                'pcc': 0.987
            },
            'time_varying_m80': {...},
            ...
        }
        save_path: 保存路径
    """
    
    # 准备数据
    prediction_data_list = []
    titles = []
    
    # 定义标题映射
    title_mapping = {
        'time_invariant_m80': 'Time-invariant,\nm = 80',
        'time_invariant_m60': 'Time-invariant,\nm = 60', 
        'time_invariant_m40': 'Time-invariant,\nm = 40',
        'time_varying_m80': 'Time-varying,\nm = 80',
        'time_varying_m60': 'Time-varying,\nm = 60',
        'time_varying_m40': 'Time-varying,\nm = 40'
    }
    
    for key, result in results_dict.items():
        data = (
            result['known_y'],
            result['label_y'], 
            result['predict_y'],
            result['loss'],
            result['pcc']
        )
        prediction_data_list.append(data)
        titles.append(title_mapping.get(key, key))
    
    # 绘制对比图
    draw_multi_prediction_comparison(
        prediction_data_list, 
        titles,
        figsize=(18, 12),
        save_path=save_path
    )

def demo_usage():
    """演示用法"""
    # 生成示例数据
    np.random.seed(42)
    
    # 模拟6个不同条件的预测结果
    results = {}
    conditions = ['time_invariant_m80', 'time_invariant_m60', 'time_invariant_m40',
                  'time_varying_m80', 'time_varying_m60', 'time_varying_m40']
    
    for condition in conditions:
        # 生成模拟数据
        train_len = 60
        predict_len = 40
        
        # 生成真实时间序列（Lorenz系统风格）
        t = np.linspace(0, 10, train_len + predict_len)
        true_signal = 10 * np.sin(t) * np.exp(-t/20) + 5 * np.cos(2*t)
        
        known_y = true_signal[:train_len]
        label_y = true_signal[train_len:]
        
        # 添加预测误差（不同条件有不同的误差水平）
        if 'time_invariant' in condition:
            noise_level = 0.5
        else:
            noise_level = 1.0
            
        if 'm80' in condition:
            noise_level *= 0.8
        elif 'm60' in condition:
            noise_level *= 1.0
        else:  # m40
            noise_level *= 1.2
            
        predict_y = label_y + np.random.normal(0, noise_level, len(label_y))
        
        # 计算指标
        loss = np.sqrt(np.mean((predict_y - label_y)**2))
        pcc = np.corrcoef(predict_y, label_y)[0, 1]
        
        results[condition] = {
            'known_y': known_y,
            'label_y': label_y,
            'predict_y': predict_y,
            'loss': loss,
            'pcc': pcc
        }
    
    # 创建对比图
    create_comparison_figure_from_results(results, 'demo_prediction_comparison.png')
    print("演示图像已生成: demo_prediction_comparison.png")

def draw_paper_style_comparison(results_dict, save_path='prediction_comparison.png'):
    """
    绘制论文风格的预测对比图（2x3布局）

    Args:
        results_dict: 包含6个条件结果的字典
        save_path: 保存路径
    """

    # 设置matplotlib参数
    plt.rcParams['figure.figsize'] = (18, 12)
    plt.rcParams['savefig.dpi'] = 300
    plt.rcParams['font.family'] = 'Times New Roman'

    # 创建2x3子图
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))

    # 定义子图顺序和标题
    subplot_order = [
        ('time_invariant_m80', 'Time-invariant,\nm = 80'),
        ('time_invariant_m60', 'Time-invariant,\nm = 60'),
        ('time_invariant_m40', 'Time-invariant,\nm = 40'),
        ('time_varying_m80', 'Time-varying,\nm = 80'),
        ('time_varying_m60', 'Time-varying,\nm = 60'),
        ('time_varying_m40', 'Time-varying,\nm = 40')
    ]

    subplot_labels = ['(a)', '(b)', '(c)', '(d)', '(e)', '(f)']

    for idx, ((key, title), label) in enumerate(zip(subplot_order, subplot_labels)):
        row = idx // 3
        col = idx % 3
        ax = axes[row, col]

        if key not in results_dict:
            ax.set_visible(False)
            continue

        result = results_dict[key]
        known_y = result['known_y']
        label_y = result['label_y']
        predict_y = result['predict_y']
        loss = result['loss']
        pcc = result['pcc']

        # 绘制预测图（模仿原始draw_pic函数）
        train_len = len(known_y)
        all_y = np.concatenate([known_y, label_y])
        x_all = np.arange(len(all_y))

        # 绘制真实值（蓝色线）
        ax.plot(x_all, all_y, color='blue', marker='.', linewidth=1.5, markersize=4)

        # 绘制预测值（红色空心圆）
        x_predict = np.arange(train_len, len(all_y))
        ax.scatter(x_predict, predict_y, color='none', edgecolors='red',
                   marker='o', s=40, linewidths=1.2, zorder=10)

        # 添加预测区域背景
        ax.axvspan(train_len, len(all_y), alpha=0.15, color='lightgreen')

        # 设置标题
        ax.set_title(title, fontsize=14, fontweight='bold', pad=10)

        # 设置坐标轴标签
        ax.set_xlabel('Time', fontsize=12)
        ax.set_ylabel('$Z_k$', fontsize=12)

        # 添加子图标签
        ax.text(0.02, 0.98, label, transform=ax.transAxes,
                fontsize=16, fontweight='bold', verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        # 添加性能指标
        metrics_text = f'RMSE: {loss:.3f}\nPCC: {pcc:.3f}'
        ax.text(0.98, 0.02, metrics_text, transform=ax.transAxes,
                fontsize=10, verticalalignment='bottom', horizontalalignment='right',
                bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8))

        # 网格
        ax.grid(True, alpha=0.3, linestyle='--')

        # 设置刻度
        ax.tick_params(axis='both', which='major', labelsize=10)

    # 调整布局
    plt.tight_layout(pad=2.0)

    # 保存图像
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"论文风格对比图已保存到: {save_path}")
    plt.close()

if __name__ == '__main__':
    demo_usage()
