#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
空间卷积模型评估脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import tensorflow as tf
import numpy as np
import pickle
from scipy.stats import pearsonr
from data import data_processing
from forecast.lorenz96 import lorenz96_config
from spatial_conv_model.st_delay_model_spatial_conv import STDelayModelSpatialConv
from forecast import st_delay_model


def evaluate_spatial_conv_model(model_path=None):
    """评估空间卷积版本的模型"""
    
    print("🔍 开始评估空间卷积版ST-Delay模型...")
    
    # 配置
    config = lorenz96_config.Lorenz96Config()
    
    # GPU配置
    gpus = tf.config.experimental.list_physical_devices(device_type='GPU')
    for gpu in gpus:
        tf.config.experimental.set_memory_growth(gpu, True)
    
    # 加载数据
    print("📊 加载Lorenz96数据...")
    data = data_processing.load_lorenz96_data(N=config.K, F=config.F, time_range=(0, 3000), dt=0.02)
    
    # 数据分割
    train_idxs, val_idxs = data_processing.get_data_idxs_as_predict_idx_no_overlap(rate=0.8, interval=60)
    
    # 添加噪声
    noised_data, y = data_processing.add_noise(data, config)
    
    # 创建数据生成器
    train_generator = st_delay_model.DataGeneratorForLengthCmp(data, y, train_idxs, config)
    val_generator = None if val_idxs is None else st_delay_model.DataGeneratorForLengthCmp(data, y, val_idxs, config)
    
    # 创建模型
    print("🏗️ 创建空间卷积版模型...")
    model = STDelayModelSpatialConv(config, mode='evaluation', log_dir_suffix='spatial_conv_model')
    
    # 加载权重
    if model_path is None:
        # 自动寻找最新的模型权重
        model_path = find_latest_spatial_conv_model()
    
    if model_path is None:
        print("❌ 未找到模型权重文件！请先训练模型。")
        return None
    
    print(f"📂 加载模型权重: {model_path}")
    
    try:
        model.load_weights(model_path)
        print("✅ 模型权重加载成功")
    except Exception as e:
        print(f"❌ 模型权重加载失败: {str(e)}")
        return None
    
    # 评估模型
    results = {}
    
    sets = {'train': train_generator, 'val': val_generator}
    
    for set_name, data_generator in sets.items():
        if data_generator is None:
            continue
            
        print(f"\n📈 评估 {set_name} 集...")
        
        losses = []
        pccs = []
        idxs = []
        
        for i in range(len(data_generator)):
            batch_data = data_generator[i]
            
            if len(batch_data) == 2:
                x, y = batch_data
            else:
                x, y = batch_data[0], batch_data[-1]
            
            # 预测
            delay_output, predict_y = model.model.predict(x, verbose=0)
            
            # 计算RMSE
            loss = np.sqrt(np.mean((predict_y - y) ** 2))
            
            # 计算PCC
            pcc = pearsonr(predict_y.flatten(), y.flatten())[0]
            
            losses.append(loss)
            pccs.append(pcc)
            idxs.append(data_generator.idxs[i])
        
        # 统计结果
        mean_loss = np.mean(losses)
        mean_pcc = np.mean(pccs)
        
        print(f"{len(losses)}")
        print(f'mean loss: {mean_loss}')
        print(f'mean pcc: {mean_pcc}')
        
        # 保存结果
        results[set_name] = {
            'losses': losses,
            'pccs': pccs,
            'idxs': idxs,
            'mean_loss': mean_loss,
            'mean_pcc': mean_pcc
        }
    
    # 保存评估结果
    result_file = 'spatial_conv_eval_results.pkl'
    with open(result_file, 'wb') as f:
        pickle.dump(results, f)
    
    print(f"\n✅ 空间卷积模型评估完成，结果已保存到 {result_file}")
    
    return results


def find_latest_spatial_conv_model():
    """寻找最新的空间卷积模型权重文件"""
    
    logs_dir = "logs"
    if not os.path.exists(logs_dir):
        return None
    
    # 寻找包含spatial_conv_model的目录
    spatial_conv_dirs = []
    for dir_name in os.listdir(logs_dir):
        if 'spatial_conv_model' in dir_name:
            dir_path = os.path.join(logs_dir, dir_name)
            if os.path.isdir(dir_path):
                spatial_conv_dirs.append(dir_path)
    
    if not spatial_conv_dirs:
        return None
    
    # 找到最新的目录
    latest_dir = max(spatial_conv_dirs, key=os.path.getmtime)
    
    # 在目录中寻找权重文件
    weight_files = []
    for file_name in os.listdir(latest_dir):
        if file_name.startswith('weights_epoch_') and file_name.endswith('.h5'):
            weight_files.append(os.path.join(latest_dir, file_name))
    
    if not weight_files:
        return None
    
    # 返回最新的权重文件
    latest_weight = max(weight_files, key=os.path.getmtime)
    return latest_weight


if __name__ == '__main__':
    try:
        results = evaluate_spatial_conv_model()
        
        if results:
            print("\n📊 评估结果汇总:")
            for set_name, result in results.items():
                print(f"{set_name.upper()} 集:")
                print(f"  RMSE: {result['mean_loss']:.6f}")
                print(f"  PCC:  {result['mean_pcc']:.6f}")
        
    except Exception as e:
        print(f"\n❌ 评估过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
