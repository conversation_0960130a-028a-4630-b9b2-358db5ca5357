#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TCN + 自适应门控混合模型
同时使用时间卷积网络(TCN)和自适应门控机制
"""

import tensorflow as tf
from tensorflow.keras.layers import Input, SpatialDropout1D
from tensorflow.keras.models import Model
from forecast.st_delay_model import STDelayModel
from utils.layers import Matrix2Y
from utils import layers

# 导入TCN和门控模块
from tcn_model.tcn_temporal import TCNTemporalModule
from adaptive_gating_model.adaptive_gating import AdaptiveGatingModule


class TCNAdaptiveGatingModel:
    """
    TCN + 自适应门控混合模型
    
    核心特性:
    1. 时间模块: TCN (时间卷积网络)
    2. 融合模块: 自适应门控机制
    3. 空间模块: 保持原始全连接网络
    """
    
    def __init__(self, config, mode='training', log_dir_suffix='tcn_adaptive_gating'):
        """
        初始化模型
        
        Args:
            config: 配置对象
            mode: 'training' 或 'prediction'
            log_dir_suffix: 日志目录后缀
        """
        self.config = config
        self.mode = mode
        
        # 确保TCN配置
        if not hasattr(config, 'TCN_FILTERS'):
            config.TCN_FILTERS = 64
        if not hasattr(config, 'USE_TCN_TEMPORAL'):
            config.USE_TCN_TEMPORAL = True
            
        # 创建日志目录
        import os
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y_%m_%d-%H_%M_%S")
        self.log_dir = f"logs/{timestamp}({config.name}_{log_dir_suffix})"
        os.makedirs(self.log_dir, exist_ok=True)
        
        # 构建模型
        self._build_model()
        
        print(" TCN+自适应门控混合模型构建完成")
        print(f" 日志目录: {self.log_dir}")
    
    def _build_model(self):
        """构建TCN+自适应门控混合模型"""
        
        # 输入层
        input_layer = Input(shape=(self.config.TRAIN_LEN, self.config.K), name='input')
        
        # Dropout层
        input_drop = SpatialDropout1D(self.config.DROP_RATE, name='input_drop')(input_layer)
        
        # 🔹 空间模块（保持原始全连接）
        spatial_features = self._build_spatial_module(input_drop)
        print(" 空间模块: 全连接网络")
        
        # 🔥 时间模块（TCN）
        temporal_features = self._build_tcn_temporal_module(input_drop)
        print(" 时间模块: TCN (时间卷积网络)")
        
        # 🚀 融合模块（自适应门控）
        merge_features = self._build_adaptive_gating_fusion(spatial_features, temporal_features)
        print(" 融合模块: 自适应门控机制")
        
        # 后续处理层
        delay_output = self._build_merge_layers(merge_features)
        
        # 输出层
        predict_y = Matrix2Y(self.config, name='predict_y')(delay_output)
        
        # 根据模式创建模型
        if self.mode == 'training':
            # 训练模式：包含损失层
            gt_y_matrix = Input(shape=(self.config.TRAIN_LEN, self.config.K), name='gt_y_matrix')
            gt_y = Input(shape=(self.config.PREDICT_LEN, self.config.K), name='gt_y')
            
            # 损失层
            from utils.layers import KnownYLoss, TimeConsistentLoss, PredictYLoss
            known_y_loss = KnownYLoss(self.config, name='known_y_loss')([gt_y_matrix, delay_output])
            consistent_loss_layer = TimeConsistentLoss(self.config, name='consistent_loss')(delay_output)
            predict_y_loss = PredictYLoss(name='predict_y_loss')([gt_y, predict_y])
            
            self.model = Model(
                inputs=[input_layer, gt_y_matrix, gt_y], 
                outputs=[delay_output, predict_y, known_y_loss, consistent_loss_layer, predict_y_loss],
                name='tcn_adaptive_gating_model'
            )
        else:
            # 评估模式
            self.model = Model(
                inputs=input_layer, 
                outputs=[delay_output, predict_y],
                name='tcn_adaptive_gating_model'
            )
        
        print("🎯 模型架构:")
        print(f"    输入维度: ({self.config.TRAIN_LEN}, {self.config.K})")
        print(f"    空间模块: 全连接网络")
        print(f"    时间模块: TCN (膨胀卷积)")
        print(f"    融合模块: 自适应门控")
        print(f"    输出维度: ({self.config.PREDICT_LEN}, {self.config.K})")
    
    def _build_spatial_module(self, input_drop):
        """构建空间模块（保持原始结构）"""
        from forecast.st_delay_model import time_distributed_graph
        
        spatial_features = time_distributed_graph(
            input_drop, 
            self.config.SPATIAL_NODES,
            last_activation=self.config.MODULE_LAST_ACITVATION,
            last_bn=True, 
            activation=self.config.ACITVATION,
            name_prefix='spatial',
            kernel_initialzer=self.config.KERNEL_INITIALIZER,
            weight_decay=self.config.WEIGHT_DECAY, 
            bn=self.config.BN
        )
        
        return spatial_features
    
    def _build_tcn_temporal_module(self, input_drop):
        """构建TCN时间模块"""
        tcn_temporal = TCNTemporalModule(self.config, name='tcn_temporal')
        temporal_features = tcn_temporal(input_drop)
        
        return temporal_features
    
    def _build_adaptive_gating_fusion(self, spatial_features, temporal_features):
        """构建自适应门控融合模块"""
        adaptive_gating_module = AdaptiveGatingModule(self.config, name='adaptive_gating_module')
        merge_features = adaptive_gating_module([spatial_features, temporal_features])
        
        return merge_features
    
    def _build_merge_layers(self, merge_features):
        """构建后续处理层"""
        from forecast.st_delay_model import time_distributed_graph
        
        delay_output = time_distributed_graph(
            merge_features, 
            self.config.MERGE_MAP_NODES, 
            last_activation=False, 
            last_bn=False, 
            activation=self.config.ACITVATION,
            name_prefix='merge_delay',
            kernel_initialzer=self.config.KERNEL_INITIALIZER,
            weight_decay=self.config.WEIGHT_DECAY, 
            bn=self.config.BN
        )
        
        return delay_output
    
    def compile(self):
        """编译模型"""
        if self.mode == 'training':
            # 训练模式的损失函数
            self.model.compile(
                optimizer=tf.keras.optimizers.Adam(learning_rate=self.config.LEARNING_RATE),
                loss=[None, None, 'mse', 'mse', 'mse'],
                loss_weights=[0, 0, self.config.KNOWN_Y_LOSS_WEIGHT, 
                             self.config.CONSISTENT_LOSS_WEIGHT, self.config.PREDICT_Y_LOSS_WEIGHT]
            )
        else:
            # 评估模式不需要损失函数
            self.model.compile(optimizer='adam')
        
        print(" 模型编译完成")
    
    def load_weights(self, weights_path):
        """加载权重"""
        self.model.load_weights(weights_path)
        print(f" 权重加载完成: {weights_path}")
    
    def train(self, train_generator, val_generator=None):
        """训练模型"""
        from tensorflow.keras.callbacks import ModelCheckpoint, EarlyStopping

        # 回调函数
        callbacks = []

        # 模型检查点
        checkpoint_path = f"{self.log_dir}/weights_epoch_{{epoch:04d}}_loss_{{val_loss:.3f}}.h5"
        checkpoint = ModelCheckpoint(
            checkpoint_path,
            monitor='val_loss',
            save_best_only=True,
            save_weights_only=True,
            verbose=1
        )
        callbacks.append(checkpoint)

        # 早停
        early_stopping = EarlyStopping(
            monitor='val_loss',
            patience=25,
            restore_best_weights=True,
            verbose=1
        )
        callbacks.append(early_stopping)

        # 开始训练
        print(" 开始训练TCN+自适应门控模型...")
        history = self.model.fit(
            train_generator,
            validation_data=val_generator,
            epochs=self.config.EPOCHS,
            callbacks=callbacks,
            verbose=1
        )

        return history
