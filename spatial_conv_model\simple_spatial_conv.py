#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单单层空间卷积模块
只用一层3x3卷积替换第一层全连接，验证空间卷积的基本合理性
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import tensorflow as tf
from tensorflow.keras.layers import Conv1D, TimeDistributed, Dense
from tensorflow.keras.layers import Layer
import numpy as np


class SimpleSpatialConvModule(Layer):
    """
    简单空间卷积模块
    只在第一层使用3x3卷积，后续保持全连接
    """
    
    def __init__(self, config, **kwargs):
        super(SimpleSpatialConvModule, self).__init__(**kwargs)
        
        self.config = config
        
        # 第一层：使用3x3卷积捕捉空间局部相关性
        self.spatial_conv1 = TimeDistributed(
            Conv1D(
                filters=512,  # 与原始对应
                kernel_size=3,
                padding='same',  # 保持序列长度
                activation='relu',
                kernel_initializer='he_normal'
            ),
            name='spatial_conv1'
        )
        
        # 第二层：保持原始全连接
        self.spatial_dense2 = TimeDistributed(
            Dense(256, activation='relu'),
            name='spatial_dense2'
        )
        
        # 第三层：保持原始全连接
        self.spatial_dense3 = TimeDistributed(
            Dense(config.K, activation='relu'),
            name='spatial_dense3'
        )
    
    def call(self, inputs, training=None):
        """前向传播"""
        # 第一层：空间卷积
        x = self.spatial_conv1(inputs, training=training)
        
        # 第二层：全连接
        x = self.spatial_dense2(x, training=training)
        
        # 第三层：全连接
        x = self.spatial_dense3(x, training=training)
        
        return x


def count_simple_spatial_conv_parameters(config):
    """统计简单空间卷积模块的参数数量"""
    K = config.K  # 60
    
    # 第一层：60 -> 512, kernel_size=3 (卷积)
    conv1_params = (3 * K * 512) + 512  # 权重 + 偏置
    
    # 第二层：512 -> 256 (全连接)
    dense2_params = (512 * 256) + 256
    
    # 第三层：256 -> 60 (全连接)
    dense3_params = (256 * K) + K
    
    total_params = conv1_params + dense2_params + dense3_params
    
    # 原始全连接参数
    original_params = (K * 512) + (512 * 256) + (256 * K)
    
    return {
        'conv1_params': conv1_params,
        'dense2_params': dense2_params,
        'dense3_params': dense3_params,
        'total_params': total_params,
        'original_params': original_params,
        'parameter_change': (total_params - original_params) / original_params,
        'conv_vs_dense_first_layer': (conv1_params - (K * 512 + 512)) / (K * 512 + 512)
    }


def analyze_spatial_conv_tradeoff(config):
    """分析空间卷积的得失权衡"""
    
    stats = count_simple_spatial_conv_parameters(config)
    
    print("🔍 单层空间卷积分析:")
    print(f"第一层卷积参数: {stats['conv1_params']:,}")
    print(f"第一层全连接参数: {config.K * 512 + 512:,}")
    print(f"第一层参数增加: {stats['conv_vs_dense_first_layer']:.1%}")
    print(f"总参数变化: {stats['parameter_change']:.1%}")
    
    # 分析得失
    print("\n⚖️ 得失权衡分析:")
    
    # 收益分析
    print("✅ 潜在收益:")
    print("  - 捕捉空间局部相关性 (相关性=0.280)")
    print("  - 更符合物理直觉 (相邻变量相互作用)")
    print("  - 平移不变性 (适合环形系统)")
    print("  - 参数共享 (可能提高泛化)")
    
    # 成本分析
    print("⚠️ 付出成本:")
    print(f"  - 参数增加 {stats['conv_vs_dense_first_layer']:.1%} (第一层)")
    print(f"  - 总参数增加 {stats['parameter_change']:.1%}")
    print("  - 计算复杂度略增")
    
    # 合理性判断
    param_increase = stats['parameter_change']
    correlation_strength = 0.280
    
    print("\n🎯 合理性判断:")
    if param_increase < 0.1 and correlation_strength > 0.2:
        print("✅ 单层空间卷积 REASONABLE")
        print("   参数增加可控 且 空间相关性显著")
    elif param_increase < 0.2 and correlation_strength > 0.15:
        print("🤔 单层空间卷积 QUESTIONABLE")
        print("   需要实验验证是否值得")
    else:
        print("❌ 单层空间卷积 NOT RECOMMENDED")
        print("   成本收益比不合理")
    
    return stats


if __name__ == "__main__":
    from forecast.lorenz96.lorenz96_config import Lorenz96Config
    
    config = Lorenz96Config()
    
    print("🔧 分析单层空间卷积的合理性...")
    stats = analyze_spatial_conv_tradeoff(config)
    
    print(f"\n📊 详细参数对比:")
    print(f"原始空间模块: {stats['original_params']:,} 参数")
    print(f"单层卷积版本: {stats['total_params']:,} 参数")
    print(f"参数变化: {stats['parameter_change']:+.1%}")
    
    # 创建模块测试
    print(f"\n🧪 创建简单空间卷积模块...")
    module = SimpleSpatialConvModule(config)
    print("✅ 模块创建成功！")
