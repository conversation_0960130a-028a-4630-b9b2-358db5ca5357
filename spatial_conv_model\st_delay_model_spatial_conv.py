#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
空间卷积版本的ST-Delay模型
只替换空间模块，时间模块保持原始Transformer
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import tensorflow as tf
from tensorflow.keras.layers import Input, Concatenate
from tensorflow.keras.models import Model
from tensorflow.keras.optimizers import Adam
import numpy as np

# 导入原始模块
from forecast.st_delay_model import STDelayModel, DataGeneratorForLengthCmp
from forecast.temporal_module import TemporalModule
from utils.layers import Matrix2Y
from utils.loss import consistent_loss

# 导入新的空间卷积模块
from spatial_conv_model.spatial_conv import SpatialConvModule


class STDelayModelSpatialConv(STDelayModel):
    """
    空间卷积版本的ST-Delay模型
    继承原始STDelayModel，只替换空间模块
    """
    
    def __init__(self, config, mode='training', log_dir_suffix='spatial_conv_model'):
        # 修改log_dir_suffix以区分模型版本
        if log_dir_suffix == 'spatial_conv_model':
            log_dir_suffix = f"{config.name}_spatial_conv_model"
        
        # 调用父类初始化，但不构建模型
        super(STDelayModel, self).__init__()  # 跳过STDelayModel的__init__
        
        self.config = config
        self.mode = mode
        self.log_dir_suffix = log_dir_suffix
        
        # 设置日志目录
        self._setup_logging()
        
        # 构建模型
        self._build_model()
    
    def _build_model(self):
        """构建模型，使用空间卷积模块"""
        
        # 输入层
        input_layer = Input(shape=(self.config.TIME_STEPS, self.config.K), name='input')
        
        # Dropout层
        from tensorflow.keras.layers import SpatialDropout1D
        input_drop = SpatialDropout1D(self.config.DROP_RATE, name='input_drop')(input_layer)
        
        # 原始空间模块（全连接）- 用于对比
        from tensorflow.keras.layers import TimeDistributed, Dense
        space_dense1 = TimeDistributed(Dense(512, activation='relu'), name='space_dense1')(input_drop)
        space_dense2 = TimeDistributed(Dense(256, activation='relu'), name='space_dense2')(space_dense1)
        space_dense3 = TimeDistributed(Dense(self.config.K, activation='relu'), name='space_dense3')(space_dense2)
        
        # 时间模块（保持原始Transformer）
        temporal_module = TemporalModule(self.config, name='temporal_module')
        temporal_output = temporal_module(input_drop)
        
        # 特征融合
        merge_features = Concatenate(axis=-1, name='merge_features')([space_dense3, temporal_output])
        
        # 新的空间卷积模块（替换后续的全连接层）
        spatial_conv_module = SpatialConvModule(self.config, name='spatial_conv_module')
        spatial_conv_output = spatial_conv_module(merge_features)
        
        # 输出层
        predict_y = Matrix2Y(self.config, name='predict_y')(spatial_conv_output)
        
        # 创建模型
        self.model = Model(inputs=input_layer, outputs=predict_y, name='st_delay_spatial_conv_model')
        
        print("✅ 空间卷积版ST-Delay模型构建完成")
        print(f"📊 模型参数统计:")
        self.model.summary()
    
    def _setup_logging(self):
        """设置日志目录"""
        import datetime
        
        current_time = datetime.datetime.now().strftime("%Y_%m_%d-%H_%M_%S")
        self.log_dir = f"logs/{current_time}({self.log_dir_suffix})"
        
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
        
        print(f"📁 日志目录: {self.log_dir}")


def create_spatial_conv_model(config, mode='training'):
    """
    创建空间卷积版本模型的工厂函数
    
    Args:
        config: 配置对象
        mode: 模式 ('training' 或 'evaluation')
        
    Returns:
        STDelayModelSpatialConv实例
    """
    return STDelayModelSpatialConv(config, mode=mode)


if __name__ == "__main__":
    # 测试代码
    from forecast.lorenz96.lorenz96_config import Lorenz96Config
    
    print("🔧 测试空间卷积版ST-Delay模型...")
    
    config = Lorenz96Config()
    
    # 创建模型
    model = create_spatial_conv_model(config, mode='training')
    
    # 编译模型
    model.compile()
    
    print("✅ 空间卷积版模型创建和编译成功！")
    
    # 显示模型结构
    print("\n📋 模型结构:")
    model.model.summary()
    
    # 参数对比
    from spatial_conv_model.spatial_conv import count_spatial_conv_parameters
    param_stats = count_spatial_conv_parameters(config)
    
    print(f"\n📊 参数对比:")
    print(f"空间卷积模块参数: {param_stats['total_params']:,}")
    print(f"原始全连接参数: {param_stats['original_dense_params']:,}")
    print(f"参数减少: {param_stats['parameter_reduction']:.1%}")
