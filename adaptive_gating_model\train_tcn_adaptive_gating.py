#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TCN + 自适应门控双重替换模型训练脚本
同时替换时间模块(Transformer→TCN)和融合模块(concat→自适应门控)
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import tensorflow as tf
from data import data_processing
from forecast.lorenz96 import lorenz96_config
from adaptive_gating_model.st_delay_model_v2 import STDelayModelV2
from forecast import st_delay_model


def train_tcn_adaptive_gating_model():
    """训练TCN+自适应门控双重替换模型"""
    
    print("🚀 开始训练TCN+自适应门控双重替换模型...")
    print("📋 替换内容:")
    print("   ✅ 时间模块: Transformer → TCN")
    print("   ✅ 融合模块: 简单concat → 自适应门控")
    
    # 配置
    config = lorenz96_config.Lorenz96Config()
    
    # 🔥 关键配置：启用TCN时间模块
    config.USE_TCN_TEMPORAL = True
    
    # TCN相关配置
    config.TCN_FILTERS = 64  # TCN滤波器数量
    
    # GPU配置
    gpus = tf.config.experimental.list_physical_devices(device_type='GPU')
    for gpu in gpus:
        tf.config.experimental.set_memory_growth(gpu, True)
    
    # 加载数据
    print("📊 加载Lorenz96数据...")
    data = data_processing.load_lorenz96_data(N=config.K, F=config.F, time_range=(0, 3000), dt=0.02)

    # 数据分割
    train_idxs, val_idxs = data_processing.get_data_idxs_as_predict_idx_no_overlap(rate=0.9, interval=60)
    print(f"📈 训练样本数: {len(train_idxs)}")
    print(f"📉 验证样本数: {len(val_idxs) if val_idxs is not None else 0}")

    # 添加噪声
    data, y = data_processing.add_noise(data, config)

    # 创建数据生成器
    train_generator = st_delay_model.DataGeneratorForLengthCmp(data, y, train_idxs, config)
    val_generator = None if val_idxs is None else st_delay_model.DataGeneratorForLengthCmp(data, y, val_idxs, config)

    # 创建TCN+自适应门控模型
    print("🏗️ 创建TCN+自适应门控模型...")
    model = STDelayModelV2(config, mode='training', log_dir_suffix='tcn_adaptive_gating_model')

    # 编译模型
    print("⚙️ 编译模型...")
    model.compile()

    # 显示模型信息
    print("\n📋 模型结构:")
    model.model.summary()

    # 参数统计
    from adaptive_gating_model.adaptive_gating import count_adaptive_gating_parameters
    param_stats = count_adaptive_gating_parameters(config)

    print(f"\n📊 模型组件统计:")
    print(f"🔹 时间模块: TCN (膨胀卷积)")
    print(f"🔹 融合模块: 自适应门控")
    print(f"🔹 门控参数量: {param_stats['total_params']:,}")
    print(f"🔹 复杂度级别: {param_stats['complexity_level']}")

    # 开始训练
    print("\n🎯 开始训练...")
    model.train(train_generator, val_generator)

    print("✅ TCN+自适应门控模型训练完成！")
    print(f"📁 模型保存在: {model.log_dir}")
    
    return model


if __name__ == '__main__':
    try:
        model = train_tcn_adaptive_gating_model()
        print("\n🎉 双重替换模型训练成功完成！")
        
    except Exception as e:
        print(f"\n❌ 训练过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
