#!/usr/bin/env python3
"""
检查各个模型的验证集样本数量
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from forecast.lorenz96 import lorenz96_config
from data import data_processing

def check_validation_samples():
    """检查不同配置下的验证集样本数"""
    
    print("=" * 60)
    print("检查各模型的验证集样本数量")
    print("=" * 60)
    
    # 加载配置
    config = lorenz96_config.Lorenz96Config()
    
    # 加载数据
    print('加载Lorenz96数据...')
    data = data_processing.load_lorenz96_data(N=config.K, F=config.F, time_range=(0, 3000), dt=0.02)
    print(f'数据总长度: {len(data)}')
    
    # 测试不同的rate配置
    rates = [0.8, 0.9, 1.0]
    
    for rate in rates:
        print(f"\n--- Rate = {rate} ---")
        train_idxs, val_idxs = data_processing.get_data_idxs_as_predict_idx_no_overlap(rate=rate, interval=60)
        
        print(f"训练样本索引数量: {len(train_idxs)}")
        print(f"验证样本索引数量: {len(val_idxs)}")
        
        # 创建数据生成器来检查实际样本数
        from forecast import st_delay_model
        
        noised_data, y = data_processing.add_noise(data, config)
        train_generator = st_delay_model.DataGeneratorForLengthCmp(data, y, train_idxs, config)
        val_generator = st_delay_model.DataGeneratorForLengthCmp(data, y, val_idxs, config) if val_idxs is not None else None
        
        print(f"训练数据生成器长度: {len(train_generator)}")
        if val_generator:
            print(f"验证数据生成器长度: {len(val_generator)}")
        else:
            print("验证数据生成器: None")
    
    print("\n" + "=" * 60)
    print("检查完成")
    print("=" * 60)

if __name__ == "__main__":
    check_validation_samples()
