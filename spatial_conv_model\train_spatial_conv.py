#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
空间卷积模型训练脚本
只改变空间模块，时间模块保持Transformer
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import tensorflow as tf
from data import data_processing
from forecast.lorenz96 import lorenz96_config
from spatial_conv_model.st_delay_model_spatial_conv import STDelayModelSpatialConv
from forecast import st_delay_model


def train_spatial_conv_model():
    """训练空间卷积版本的模型"""
    
    print("🚀 开始训练空间卷积版ST-Delay模型...")
    
    # 配置
    config = lorenz96_config.Lorenz96Config()
    
    # GPU配置
    gpus = tf.config.experimental.list_physical_devices(device_type='GPU')
    for gpu in gpus:
        tf.config.experimental.set_memory_growth(gpu, True)
    
    # 加载数据
    print("📊 加载Lorenz96数据...")
    data = data_processing.load_lorenz96_data(N=config.K, F=config.F, time_range=(0, 3000), dt=0.02)
    
    # 数据分割
    train_idxs, val_idxs = data_processing.get_data_idxs_as_predict_idx_no_overlap(rate=0.8, interval=60)
    print(f"训练样本数: {len(train_idxs)}")
    print(f"验证样本数: {len(val_idxs) if val_idxs is not None else 0}")
    
    # 添加噪声
    data, y = data_processing.add_noise(data, config)
    
    # 创建数据生成器
    train_generator = st_delay_model.DataGeneratorForLengthCmp(data, y, train_idxs, config)
    val_generator = None if val_idxs is None else st_delay_model.DataGeneratorForLengthCmp(data, y, val_idxs, config)
    
    # 创建空间卷积模型
    print("🏗️ 创建空间卷积版模型...")
    model = STDelayModelSpatialConv(config, mode='training', log_dir_suffix='spatial_conv_model')
    
    # 编译模型
    print("⚙️ 编译模型...")
    model.compile()
    
    # 显示模型信息
    print("\n📋 模型结构:")
    model.model.summary()
    
    # 开始训练
    print("\n🎯 开始训练...")
    model.train(train_generator, val_generator)
    
    print("✅ 空间卷积模型训练完成！")
    print(f"📁 模型保存在: {model.log_dir}")
    
    return model


if __name__ == '__main__':
    try:
        model = train_spatial_conv_model()
        print("\n🎉 训练成功完成！")
        
    except Exception as e:
        print(f"\n❌ 训练过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
