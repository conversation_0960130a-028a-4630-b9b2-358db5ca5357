#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自适应交互门控模块
用于替换原始的简单concat融合，学习空间-时间特征的非线性交互
"""

import tensorflow as tf
from tensorflow.keras.layers import Dense, Concatenate, Dropout, LayerNormalization, Layer
from tensorflow.keras.models import Sequential
import numpy as np


class AdaptiveInteractionGating(Layer):
    """
    自适应交互门控层 - 完整版本

    核心思想：
    1. 学习空间-时间特征的深层非线性交互
    2. 基于系统状态自适应调整融合策略
    3. 多层次特征融合与门控
    4. 动态路由机制
    """

    def __init__(self, feature_dim, dropout_rate=0.1, num_interaction_layers=3, **kwargs):
        super(AdaptiveInteractionGating, self).__init__(**kwargs)

        self.feature_dim = feature_dim
        self.dropout_rate = dropout_rate
        self.num_interaction_layers = num_interaction_layers
        
        # 深层特征变换网络
        self.spatial_transform = Sequential([
            Dense(feature_dim, activation='relu', kernel_initializer='he_normal'),
            LayerNormalization(),
            Dropout(dropout_rate),
            Dense(feature_dim, activation='tanh', kernel_initializer='he_normal')
        ], name='spatial_transform')

        self.temporal_transform = Sequential([
            Dense(feature_dim, activation='relu', kernel_initializer='he_normal'),
            LayerNormalization(),
            Dropout(dropout_rate),
            Dense(feature_dim, activation='tanh', kernel_initializer='he_normal')
        ], name='temporal_transform')
        
        # 深层交互学习网络 - 学习复杂的空间-时间交互模式
        self.interaction_layers = []
        for i in range(num_interaction_layers):
            layer = Sequential([
                Dense(feature_dim * 2, activation='relu', kernel_initializer='he_normal'),
                LayerNormalization(),
                Dropout(dropout_rate),
                Dense(feature_dim, activation='tanh', kernel_initializer='he_normal'),
                Dropout(dropout_rate),
                Dense(feature_dim, activation='relu', kernel_initializer='he_normal')
            ], name=f'interaction_layer_{i}')
            self.interaction_layers.append(layer)

        # 交互特征融合
        self.interaction_fusion = Sequential([
            Dense(feature_dim, activation='tanh', kernel_initializer='he_normal'),
            LayerNormalization(),
            Dense(feature_dim, activation='relu', kernel_initializer='he_normal')
        ], name='interaction_fusion')
        
        # 复杂门控网络 - 真正的自适应门控
        self.spatial_gate_net = Sequential([
            Dense(feature_dim // 2, activation='relu', kernel_initializer='he_normal'),
            LayerNormalization(),
            Dropout(dropout_rate),
            Dense(feature_dim // 4, activation='tanh', kernel_initializer='he_normal'),
            Dense(1, activation='sigmoid', kernel_initializer='glorot_uniform')
        ], name='spatial_gate_net')

        self.temporal_gate_net = Sequential([
            Dense(feature_dim // 2, activation='relu', kernel_initializer='he_normal'),
            LayerNormalization(),
            Dropout(dropout_rate),
            Dense(feature_dim // 4, activation='tanh', kernel_initializer='he_normal'),
            Dense(1, activation='sigmoid', kernel_initializer='glorot_uniform')
        ], name='temporal_gate_net')

        self.interaction_gate_net = Sequential([
            Dense(feature_dim // 2, activation='relu', kernel_initializer='he_normal'),
            LayerNormalization(),
            Dropout(dropout_rate),
            Dense(feature_dim // 4, activation='tanh', kernel_initializer='he_normal'),
            Dense(1, activation='sigmoid', kernel_initializer='glorot_uniform')
        ], name='interaction_gate_net')
        
        # 动态路由融合网络 - 自适应学习最优融合策略
        self.dynamic_routing = Sequential([
            Dense(feature_dim, activation='relu', kernel_initializer='he_normal'),
            LayerNormalization(),
            Dropout(dropout_rate),
            Dense(feature_dim // 2, activation='tanh', kernel_initializer='he_normal'),
            Dropout(dropout_rate),
            Dense(feature_dim // 4, activation='relu', kernel_initializer='he_normal'),
            Dense(3, activation='softmax', kernel_initializer='glorot_uniform')  # 3个分支的动态权重
        ], name='dynamic_routing')

        # 状态感知权重调整
        self.state_aware_adjustment = Sequential([
            Dense(feature_dim // 2, activation='relu', kernel_initializer='he_normal'),
            Dense(3, activation='softmax', kernel_initializer='glorot_uniform')
        ], name='state_aware_adjustment')
        
        # 交互特征投影层
        self.interaction_projection = Dense(
            feature_dim,
            activation='tanh',
            kernel_initializer='he_normal',
            name='interaction_projection'
        )

        # 输出投影
        self.output_projection = Dense(
            feature_dim,
            activation='tanh',
            kernel_initializer='he_normal',
            name='output_projection'
        )
    
    def call(self, inputs, training=None):
        """
        完整的自适应交互门控前向传播

        Args:
            inputs: [spatial_features, temporal_features]
            training: 是否为训练模式

        Returns:
            融合后的特征
        """
        spatial_feat, temporal_feat = inputs

        # 阶段1: 深层特征变换
        spatial_transformed = self.spatial_transform(spatial_feat, training=training)
        temporal_transformed = self.temporal_transform(temporal_feat, training=training)

        # 阶段2: 多层交互学习 - 学习复杂的空间-时间交互模式
        interaction_features = []
        current_input = Concatenate(axis=-1)([spatial_transformed, temporal_transformed])

        for i, interaction_layer in enumerate(self.interaction_layers):
            interaction_output = interaction_layer(current_input, training=training)
            interaction_features.append(interaction_output)
            # 残差连接用于下一层
            if i < len(self.interaction_layers) - 1:
                current_input = Concatenate(axis=-1)([current_input, interaction_output])

        # 融合所有交互特征
        if len(interaction_features) > 1:
            interaction_combined = Concatenate(axis=-1)(interaction_features)
        else:
            interaction_combined = interaction_features[0]

        interaction_final = self.interaction_fusion(interaction_combined, training=training)

        # 投影到正确维度
        interaction_projected = self.interaction_projection(interaction_final)

        # 阶段3: 真正的自适应门控 - 不是简单的乘法！
        # 计算门控权重
        spatial_gate_weight = self.spatial_gate_net(
            Concatenate(axis=-1)([spatial_transformed, temporal_transformed, interaction_projected]),
            training=training
        )
        temporal_gate_weight = self.temporal_gate_net(
            Concatenate(axis=-1)([temporal_transformed, spatial_transformed, interaction_projected]),
            training=training
        )
        interaction_gate_weight = self.interaction_gate_net(
            Concatenate(axis=-1)([interaction_projected, spatial_transformed, temporal_transformed]),
            training=training
        )

        # 真正的门控融合 - 每个特征都受到其他特征的影响
        spatial_gated = (spatial_gate_weight * spatial_feat +
                        (1 - spatial_gate_weight) * 0.5 * (temporal_feat + interaction_projected))

        temporal_gated = (temporal_gate_weight * temporal_feat +
                         (1 - temporal_gate_weight) * 0.5 * (spatial_feat + interaction_projected))

        interaction_gated = (interaction_gate_weight * interaction_projected +
                           (1 - interaction_gate_weight) * 0.5 * (spatial_feat + temporal_feat))

        # 阶段4: 动态路由融合 - 自适应学习最优融合策略
        # 计算系统状态特征
        system_state = Concatenate(axis=-1)([spatial_gated, temporal_gated, interaction_gated])

        # 动态路由权重
        routing_weights = self.dynamic_routing(system_state, training=training)  # [batch, time, 3]

        # 状态感知调整
        state_adjustment = self.state_aware_adjustment(
            tf.reduce_mean(system_state, axis=1, keepdims=True), training=training
        )  # [batch, 1, 3]

        # 最终融合权重 = 动态路由权重 * 状态调整权重
        final_weights = routing_weights * state_adjustment

        # 归一化权重
        final_weights = tf.nn.softmax(final_weights, axis=-1)

        # 阶段5: 最终自适应融合
        w1 = tf.expand_dims(final_weights[:, :, 0], axis=-1)  # [batch, time, 1]
        w2 = tf.expand_dims(final_weights[:, :, 1], axis=-1)  # [batch, time, 1]
        w3 = tf.expand_dims(final_weights[:, :, 2], axis=-1)  # [batch, time, 1]

        fused_output = (w1 * spatial_gated +
                       w2 * temporal_gated +
                       w3 * interaction_gated)

        # 阶段6: 输出投影与残差连接
        output = self.output_projection(fused_output)

        # 全局残差连接
        output = output + 0.1 * (spatial_feat + temporal_feat)

        return output
    
    def get_config(self):
        config = super(AdaptiveInteractionGating, self).get_config()
        config.update({
            'feature_dim': self.feature_dim,
            'dropout_rate': self.dropout_rate,
            'num_interaction_layers': self.num_interaction_layers
        })
        return config


class AdaptiveGatingModule(Layer):
    """
    自适应门控模块
    替换原始的简单concat融合
    """
    
    def __init__(self, config, **kwargs):
        super(AdaptiveGatingModule, self).__init__(**kwargs)
        
        self.config = config
        
        # 获取特征维度（假设空间和时间特征维度相同）
        self.feature_dim = config.K  # 60
        
        # 自适应交互门控
        self.adaptive_gating = AdaptiveInteractionGating(
            feature_dim=self.feature_dim,
            dropout_rate=config.DROP_RATE,
            name='adaptive_interaction_gating'
        )
        
        # 残差连接的投影层（如果需要）
        self.residual_projection = Dense(
            self.feature_dim,
            activation=None,
            kernel_initializer='he_normal',
            name='residual_projection'
        )
    
    def call(self, inputs, training=None):
        """
        前向传播
        
        Args:
            inputs: [spatial_features, temporal_features]
            training: 是否为训练模式
            
        Returns:
            融合后的特征
        """
        spatial_features, temporal_features = inputs
        
        # 自适应门控融合
        gated_output = self.adaptive_gating([spatial_features, temporal_features], training=training)
        
        # 残差连接（将原始特征投影后相加）
        spatial_residual = self.residual_projection(spatial_features)
        temporal_residual = self.residual_projection(temporal_features)
        
        # 最终输出：门控输出 + 残差连接
        output = gated_output + 0.5 * (spatial_residual + temporal_residual)
        
        return output
    
    def get_config(self):
        config = super(AdaptiveGatingModule, self).get_config()
        config.update({
            'config': self.config
        })
        return config


def create_adaptive_gating_module(config):
    """
    创建自适应门控模块的工厂函数
    
    Args:
        config: 配置对象
        
    Returns:
        AdaptiveGatingModule实例
    """
    return AdaptiveGatingModule(config)


def count_adaptive_gating_parameters(config):
    """
    统计完整版自适应门控模块的参数数量

    Args:
        config: 配置对象

    Returns:
        dict: 参数统计信息
    """
    K = config.K  # 60
    num_interaction_layers = 3

    # 深层特征变换网络 (每个都是4层网络)
    # 空间变换: 60->60->60->60, 时间变换: 60->60->60->60
    spatial_transform_params = (K*K + K) + (K*K + K) + (K*K + K) + (K*K + K)  # 4层
    temporal_transform_params = (K*K + K) + (K*K + K) + (K*K + K) + (K*K + K)  # 4层

    # 多层交互学习网络 (3层，每层6个子层)
    interaction_params = 0
    for i in range(num_interaction_layers):
        # 每个交互层: 120->120, 120->60, 60->60 (6个Dense层)
        layer_params = ((K*2)*(K*2) + (K*2)) + ((K*2)*K + K) + (K*K + K) + \
                      (K*K + K) + (K*K + K) + (K*K + K)
        interaction_params += layer_params

    # 交互融合网络 (3层)
    interaction_fusion_params = (K*3)*K + K + K*K + K + K*K + K  # 3层

    # 复杂门控网络 (每个5层，共3个)
    gate_params = 0
    for _ in range(3):  # 3个门控网络
        # 每个门控: 180->(K//2)->BN->(K//4)->1 (5层)
        gate_params += ((K*3)*(K//2) + (K//2)) + ((K//2)*(K//4) + (K//4)) + (K//4)*1 + 1

    # 动态路由网络 (7层)
    routing_params = (K*3)*K + K + (K*(K//2) + (K//2)) + ((K//2)*(K//4) + (K//4)) + \
                    ((K//4)*3 + 3) + (K*K + K) + ((K//2)*3 + 3)

    # 投影层
    interaction_proj_params = K*K + K  # 60 -> 60
    output_proj_params = K*K + K  # 60 -> 60
    residual_proj_params = K*K + K  # 60 -> 60

    total_params = (spatial_transform_params + temporal_transform_params +
                   interaction_params + interaction_fusion_params +
                   gate_params + routing_params +
                   interaction_proj_params + output_proj_params + residual_proj_params)

    # 原始concat的参数（基本为0，只是简单拼接）
    original_concat_params = 0

    return {
        'transform_params': spatial_transform_params + temporal_transform_params,
        'interaction_params': interaction_params + interaction_fusion_params,
        'gate_params': gate_params,
        'routing_params': routing_params,
        'projection_params': interaction_proj_params + output_proj_params + residual_proj_params,
        'total_params': total_params,
        'original_concat_params': original_concat_params,
        'parameter_increase': total_params,  # 相对于concat的增加
        'complexity_level': 'FULL_ADAPTIVE_INTERACTION_GATING'
    }


if __name__ == "__main__":
    # 测试代码
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    from forecast.lorenz96.lorenz96_config import Lorenz96Config
    
    config = Lorenz96Config()
    
    # 创建模块
    gating_module = create_adaptive_gating_module(config)
    
    # 参数统计
    param_stats = count_adaptive_gating_parameters(config)
    
    print("完整版自适应交互门控模块参数统计:")
    print(f"复杂度级别: {param_stats['complexity_level']}")
    print(f"深层特征变换参数: {param_stats['transform_params']:,}")
    print(f"多层交互网络参数: {param_stats['interaction_params']:,}")
    print(f"复杂门控网络参数: {param_stats['gate_params']:,}")
    print(f"动态路由网络参数: {param_stats['routing_params']:,}")
    print(f"投影层参数: {param_stats['projection_params']:,}")
    print(f"总参数量: {param_stats['total_params']:,}")
    print(f"相对简单concat增加: {param_stats['parameter_increase']:,} 参数")
    print(f"参数复杂度提升: {param_stats['total_params'] / 46566:.1f}x")

    print("\n自适应交互门控模块创建成功！")
