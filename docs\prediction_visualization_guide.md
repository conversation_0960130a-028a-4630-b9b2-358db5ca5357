# 📊 预测图绘制指南

## 🎯 概述

本指南详细说明了如何绘制类似您展示的预测对比图。原始模型使用 `draw_pic` 函数来绘制单个预测图，我们扩展了这个功能来支持多子图对比。

## 🔍 原始模型的绘图机制

### 核心函数：`draw_pic`

原始模型中的预测图是通过 `draw_pic` 函数绘制的，该函数位于各个评估文件中：

```python
# 位置：forecast/lorenz96/eval.py, forecast/ks/eval.py 等
def draw_pic(known_y, label_y, predict_y, loss, pcc, x_label=None, y_label=None, y_lim=None,
             title=None, path=None, figsize=None):
```

### 绘图逻辑解析

<augment_code_snippet path="forecast/lorenz96/eval.py" mode="EXCERPT">
````python
def draw_pic(known_y, label_y, predict_y, loss, pcc, x_label=None, y_label=None, y_lim=None,
             title=None, path=None, figsize=None):
    plt.rcParams['figure.figsize'] = figsize 
    plt.rcParams['savefig.dpi'] = 200

    fontsize = 17
    plt.title(title + ", PCC:{:.2f}".format(pcc), fontdict={'family': 'Times New Roman', 'size': fontsize})
    plt.xlabel(x_label, fontdict={'family': 'Times New Roman', 'size': fontsize})
    plt.ylabel(y_label, fontdict={'family': 'Times New Roman', 'size': fontsize})
    
    # 绘制完整的真实值序列（已知部分 + 标签部分）
    train_len = len(known_y)
    all_y = np.concatenate([known_y, label_y])
    x = np.arange(len(all_y))
    plt.plot(x, all_y, color='blue', marker='.')

    # 绘制预测值（只在预测区域）
    x = np.arange(train_len, len(all_y))
    plt.scatter(x, predict_y, color='none', edgecolors='red', marker='o',
                label='trained_loss:{:.2f},pcc:{:.2f}'.format(loss, pcc),
                zorder=10, linewidths=1.2, s=40)
````
</augment_code_snippet>

### 关键特征

1. **🔵 蓝色线条**：完整的真实值序列（训练部分 + 预测目标）
2. **🔴 红色圆圈**：模型预测值（空心圆圈，只在预测区域显示）
3. **📊 性能指标**：显示RMSE损失和PCC相关系数
4. **🎨 字体设置**：使用Times New Roman字体，字号17

## 🚀 新增的多子图功能

### 1. 论文风格对比图

我们创建了 `draw_paper_style_comparison` 函数来生成类似您展示的2×3布局对比图：

```python
from utils.multi_subplot_visualization import draw_paper_style_comparison

# 准备数据字典
results = {
    'time_invariant_m80': {
        'known_y': [...],    # 已知的训练数据
        'label_y': [...],    # 真实的预测目标
        'predict_y': [...],  # 模型预测结果
        'loss': 0.123,       # RMSE损失
        'pcc': 0.987         # 皮尔逊相关系数
    },
    'time_invariant_m60': {...},
    'time_invariant_m40': {...},
    'time_varying_m80': {...},
    'time_varying_m60': {...},
    'time_varying_m40': {...}
}

# 生成对比图
draw_paper_style_comparison(results, 'comparison.png')
```

### 2. 通用多子图功能

```python
from utils.multi_subplot_visualization import create_comparison_figure_from_results

# 自动布局的多子图对比
create_comparison_figure_from_results(results, 'general_comparison.png')
```

## 📈 数据格式说明

### 输入数据结构

每个预测结果需要包含以下5个组件：

```python
{
    'known_y': np.array,     # 形状: (train_len,) - 训练时已知的数据
    'label_y': np.array,     # 形状: (predict_len,) - 预测的真实目标
    'predict_y': np.array,   # 形状: (predict_len,) - 模型预测结果
    'loss': float,           # RMSE损失值
    'pcc': float            # 皮尔逊相关系数
}
```

### 时间轴说明

- **时间 0 到 train_len**：训练阶段，显示已知数据（蓝色线）
- **时间 train_len 到 total_len**：预测阶段，显示真实值（蓝色线）和预测值（红色圆圈）

## 🎨 视觉元素详解

### 颜色方案
- **蓝色 (`blue`)**：真实值序列
- **红色 (`red`)**：预测值（空心圆圈）
- **浅绿色背景**：预测区域标识

### 标记样式
- **真实值**：实线 + 小圆点标记
- **预测值**：空心圆圈，边框红色，zorder=10确保在最上层

### 文本信息
- **子图标签**：(a), (b), (c), (d), (e), (f)
- **标题**：Time-invariant/Time-varying + 参数信息
- **性能指标**：RMSE和PCC值，显示在右下角

## 🛠️ 使用示例

### 完整使用流程

```python
# 1. 导入必要模块
import numpy as np
from utils.multi_subplot_visualization import draw_paper_style_comparison

# 2. 准备预测数据（示例）
def prepare_prediction_data():
    results = {}
    
    # 为每个条件准备数据
    for condition in ['time_invariant_m80', 'time_varying_m80', ...]:
        # 从模型评估中获取数据
        known_y = ...      # 训练数据
        label_y = ...      # 真实预测目标  
        predict_y = ...    # 模型预测结果
        
        # 计算指标
        loss = np.sqrt(np.mean((predict_y - label_y)**2))
        pcc = np.corrcoef(predict_y, label_y)[0, 1]
        
        results[condition] = {
            'known_y': known_y,
            'label_y': label_y,
            'predict_y': predict_y,
            'loss': loss,
            'pcc': pcc
        }
    
    return results

# 3. 生成对比图
results = prepare_prediction_data()
draw_paper_style_comparison(results, 'my_comparison.png')
```

### 从模型评估中提取数据

如果您已经运行了模型评估，可以从保存的结果中提取数据：

```python
import pickle

# 加载评估结果
with open('logs/results/model_prediction_results.pkl', 'rb') as f:
    eval_results = pickle.load(f)

# 转换为绘图格式
results = {}
for i, (known_y, label_y, predict_y) in enumerate(zip(
    eval_results['train_ys'], 
    eval_results['label_ys'], 
    eval_results['predict_ys']
)):
    if i < 6:  # 只取前6个样本
        condition = f'sample_{i}'
        loss = eval_results['rmses'][i]
        pcc = eval_results['pccs'][i]
        
        results[condition] = {
            'known_y': known_y,
            'label_y': label_y,
            'predict_y': predict_y,
            'loss': loss,
            'pcc': pcc
        }

# 生成对比图
draw_paper_style_comparison(results, 'evaluation_comparison.png')
```

## 📁 文件结构

```
utils/
├── multi_subplot_visualization.py  # 多子图绘制功能
examples/
├── create_prediction_comparison.py # 使用示例
docs/
├── prediction_visualization_guide.md # 本指南
```

## 🔧 自定义选项

### 调整图像参数

```python
# 自定义图像大小和分辨率
draw_paper_style_comparison(
    results, 
    save_path='custom_comparison.png',
    figsize=(20, 14),  # 自定义尺寸
    dpi=400           # 高分辨率
)
```

### 修改颜色和样式

可以在 `utils/multi_subplot_visualization.py` 中修改：

```python
# 修改颜色
ax.plot(x_all, all_y, color='navy', ...)        # 改变真实值颜色
ax.scatter(..., edgecolors='crimson', ...)       # 改变预测值颜色

# 修改标记样式
ax.scatter(..., marker='s', s=60, ...)          # 使用方形标记
```

## ❓ 常见问题

**Q: 如何确保预测图的时间轴正确？**
A: 确保 `known_y` 的长度等于训练长度，`label_y` 和 `predict_y` 的长度相等且为预测长度。

**Q: 如何处理不同长度的时间序列？**
A: 可以通过插值或截断来统一长度，或者为每个子图单独设置时间轴范围。

**Q: 如何添加更多性能指标？**
A: 在 `_draw_single_prediction` 函数中修改 `metrics_text` 变量，添加更多指标如MAE、MAPE等。

## 🎯 总结

原始模型的预测图通过 `draw_pic` 函数实现，我们扩展了这个功能来支持多子图对比，完美复现了您展示的论文风格图像。关键是理解数据的时间结构和视觉元素的含义，然后正确组织数据并调用相应的绘图函数。
