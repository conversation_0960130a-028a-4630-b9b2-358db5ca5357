from configs import config


class KSConfig(config.Config):
    def __init__(self):
        super(KSConfig, self).__init__()

        # ##################
        # # for KS #
        # ##################
        self.MODEL_NAME = 'KS'  # the name of data

        self.DROP_RATE = 0

        self.EMBEDDING_LEN = 14

        self.K = 512

        self.TRAIN_LEN = 40

        self.SPATIAL_NODES = [512, 256, self.K]

        self.MERGE_MAP_NODES = [512, 256, 128, self.EMBEDDING_LEN]

        self.Y_IDX = 0

        self.MODULE_LAST_ACITVATION = False

        self.DATASET_RATE = 0

        self.TRAINING = True  

        self.ENCODING_LAYER_NUMS = 2 

        self.TEMPORAL_DIM = self.K 

        self.DIFF = self.TEMPORAL_DIM * 4

        self.NUM_HEADS = 16

        self.BATCH_SIZE = 4

        self.EPOCHS = 85

        self.LOSS_WEIGHTS = {'consistent_loss': 1}

        self.MERGE_ONLY = False  

        self.MERGE_FUNC = 'concat'  # 'add'

        # TCN相关配置
        self.USE_TCN_TEMPORAL = False  # TCN时间模块开关
        self.TCN_FILTERS = 64          # TCN卷积滤波器数量

        self.LR = 1e-3

        self.WEIGHT_DECAY = 0  

        self.BN = False

        self.ADD_NOISE = False

        self.DATA_NOISE_STRENGTH = 0

        if self.MERGE_ONLY:
            self.MODEL_NAME = self.MODEL_NAME + '_merge_only'

    def LR_SCHEDULER(self, epoch):
        if epoch <= 20:
            return self.LR
        elif epoch <= 40:
            return self.LR / 3.0
        elif epoch <= 65:
            return self.LR / 10.0
        else:
            return self.LR / 30.0


    @property
    def name(self):
        if self.ADD_NOISE:
            return '{}_{}_{}_Yidx_{}_noise_{}'.format(self.MODEL_NAME, self.TRAIN_LEN, self.EMBEDDING_LEN, self.Y_IDX, self.DATA_NOISE_STRENGTH)
        else:
            return '{}_{}_{}_Yidx_{}'.format(self.MODEL_NAME, self.TRAIN_LEN, self.EMBEDDING_LEN, self.Y_IDX)
