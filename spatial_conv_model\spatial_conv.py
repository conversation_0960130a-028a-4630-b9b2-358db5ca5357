#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
空间卷积模块
用1D卷积替换原始的时间分布式全连接层，专门处理Lorenz96的空间相关性
"""

import tensorflow as tf
from tensorflow.keras.layers import Conv1D, TimeDistributed, Dropout, BatchNormalization
from tensorflow.keras.layers import Layer, Dense
import numpy as np


class SpatialConv1D(Layer):
    """
    空间1D卷积层
    专门为Lorenz96系统设计，考虑环形边界条件
    """
    
    def __init__(self, filters, kernel_size=3, activation='relu', dropout_rate=0.0, 
                 use_batch_norm=False, **kwargs):
        super(SpatialConv1D, self).__init__(**kwargs)
        
        self.filters = filters
        self.kernel_size = kernel_size
        self.activation = activation
        self.dropout_rate = dropout_rate
        self.use_batch_norm = use_batch_norm
        
        # 卷积层 - 使用same padding处理环形边界
        self.conv1d = Conv1D(
            filters=filters,
            kernel_size=kernel_size,
            padding='same',  # 保持序列长度不变
            activation=None,  # 激活函数单独处理
            kernel_initializer='he_normal'
        )
        
        # 批归一化（可选）
        if self.use_batch_norm:
            self.batch_norm = BatchNormalization()
        
        # 激活函数
        if activation == 'relu':
            self.activation_fn = tf.nn.relu
        elif activation == 'tanh':
            self.activation_fn = tf.nn.tanh
        elif activation == 'swish':
            self.activation_fn = tf.nn.swish
        else:
            self.activation_fn = tf.nn.relu
            
        # Dropout（可选）
        if dropout_rate > 0:
            self.dropout = Dropout(dropout_rate)
    
    def call(self, inputs, training=None):
        # 卷积操作
        x = self.conv1d(inputs)
        
        # 批归一化
        if self.use_batch_norm:
            x = self.batch_norm(x, training=training)
        
        # 激活函数
        x = self.activation_fn(x)
        
        # Dropout
        if self.dropout_rate > 0:
            x = self.dropout(x, training=training)
            
        return x
    
    def get_config(self):
        config = super(SpatialConv1D, self).get_config()
        config.update({
            'filters': self.filters,
            'kernel_size': self.kernel_size,
            'activation': self.activation,
            'dropout_rate': self.dropout_rate,
            'use_batch_norm': self.use_batch_norm
        })
        return config


class SpatialConvModule(Layer):
    """
    空间卷积模块
    替换原始的时间分布式全连接层
    """
    
    def __init__(self, config, **kwargs):
        super(SpatialConvModule, self).__init__(**kwargs)
        
        self.config = config
        
        # 根据分析结果，使用简单的3层卷积结构
        # 主要捕捉局部空间相关性 (kernel_size=3)
        
        # 第一层：扩展特征维度 (对应原始的512)
        self.spatial_conv1 = TimeDistributed(
            SpatialConv1D(
                filters=512,  # 与原始完全对应
                kernel_size=3,
                activation='relu',
                dropout_rate=config.DROP_RATE,
                use_batch_norm=False  # 原始没有BN
            ),
            name='spatial_conv1'
        )

        # 第二层：特征提取 (对应原始的256)
        self.spatial_conv2 = TimeDistributed(
            SpatialConv1D(
                filters=256,  # 与原始完全对应
                kernel_size=1,  # 使用1x1卷积减少参数
                activation='relu',
                dropout_rate=config.DROP_RATE,
                use_batch_norm=False  # 原始没有BN
            ),
            name='spatial_conv2'
        )
        
        # 第三层：输出映射 (对应原始的60)
        self.spatial_conv3 = TimeDistributed(
            SpatialConv1D(
                filters=config.K,  # 输出维度 = 60
                kernel_size=1,     # 1x1卷积用于维度变换
                activation='relu',
                dropout_rate=0,    # 最后一层不用dropout
                use_batch_norm=False
            ),
            name='spatial_conv3'
        )
    
    def call(self, inputs, training=None):
        """
        前向传播
        
        Args:
            inputs: shape (batch_size, time_steps, spatial_dim)
            training: 是否为训练模式
            
        Returns:
            outputs: shape (batch_size, time_steps, spatial_dim)
        """
        # 第一层卷积：60 -> 256
        x = self.spatial_conv1(inputs, training=training)
        
        # 第二层卷积：256 -> 128  
        x = self.spatial_conv2(x, training=training)
        
        # 第三层卷积：128 -> 60
        x = self.spatial_conv3(x, training=training)
        
        return x
    
    def get_config(self):
        config = super(SpatialConvModule, self).get_config()
        config.update({
            'config': self.config
        })
        return config


def create_spatial_conv_module(config):
    """
    创建空间卷积模块的工厂函数
    
    Args:
        config: 配置对象
        
    Returns:
        SpatialConvModule实例
    """
    return SpatialConvModule(config)


# 参数统计函数
def count_spatial_conv_parameters(config):
    """
    统计空间卷积模块的参数数量
    
    Args:
        config: 配置对象
        
    Returns:
        dict: 参数统计信息
    """
    K = config.K  # 60
    
    # 第一层：60 -> 512, kernel_size=3
    conv1_params = (3 * K * 512) + 512  # 权重 + 偏置

    # 第二层：512 -> 256, kernel_size=1
    conv2_params = (1 * 512 * 256) + 256
    
    # 第三层：256 -> 60, kernel_size=1
    conv3_params = (1 * 256 * K) + K

    # 批归一化参数 (原始没有BN，所以为0)
    bn_params = 0
    
    total_params = conv1_params + conv2_params + conv3_params + bn_params
    
    return {
        'conv1_params': conv1_params,
        'conv2_params': conv2_params, 
        'conv3_params': conv3_params,
        'bn_params': bn_params,
        'total_params': total_params,
        'original_dense_params': (K * 512) + (512 * 256) + (256 * K),  # 原始全连接参数
        'parameter_reduction': 1 - (total_params / ((K * 512) + (512 * 256) + (256 * K)))
    }


if __name__ == "__main__":
    # 测试代码
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

    from forecast.lorenz96.lorenz96_config import Lorenz96Config
    
    config = Lorenz96Config()
    
    # 创建模块
    spatial_module = create_spatial_conv_module(config)
    
    # 参数统计
    param_stats = count_spatial_conv_parameters(config)
    
    print("🔧 空间卷积模块参数统计:")
    print(f"第一层卷积参数: {param_stats['conv1_params']:,}")
    print(f"第二层卷积参数: {param_stats['conv2_params']:,}")
    print(f"第三层卷积参数: {param_stats['conv3_params']:,}")
    print(f"批归一化参数: {param_stats['bn_params']:,}")
    print(f"总参数量: {param_stats['total_params']:,}")
    print(f"原始全连接参数: {param_stats['original_dense_params']:,}")
    print(f"参数减少比例: {param_stats['parameter_reduction']:.1%}")
    
    print("\n✅ 空间卷积模块创建成功！")
