"""
如何使用原始模型的draw_pic函数绘制预测图

这个脚本展示了原始模型中draw_pic函数的使用方法，
以及如何从eval脚本运行后获取预测图片。
"""

import numpy as np
import matplotlib
matplotlib.use('agg')  # 使用非交互式后端
import matplotlib.pyplot as plt

def draw_pic(known_y, label_y, predict_y, loss, pcc, x_label=None, y_label=None, y_lim=None,
             title=None, path=None, figsize=None):
    """
    原始模型的draw_pic函数（从forecast/lorenz96/eval.py复制）
    
    Args:
        known_y: 训练时已知的数据序列
        label_y: 预测的真实目标序列  
        predict_y: 模型预测结果序列
        loss: RMSE损失值
        pcc: 皮尔逊相关系数
        x_label: X轴标签
        y_label: Y轴标签
        y_lim: Y轴范围
        title: 图片标题
        path: 保存路径
        figsize: 图片尺寸
    """
    plt.rcParams['figure.figsize'] = figsize 
    plt.rcParams['savefig.dpi'] = 200

    fontsize = 17
    plt.title(title + ", PCC:{:.2f}".format(pcc), fontdict={'family': 'Times New Roman', 'size': fontsize})
    plt.xlabel(x_label, fontdict={'family': 'Times New Roman', 'size': fontsize})
    plt.ylabel(y_label, fontdict={'family': 'Times New Roman', 'size': fontsize})
    plt.yticks(fontproperties='Times New Roman', size=fontsize)
    plt.xticks(fontproperties='Times New Roman', size=fontsize)

    if y_lim is not None:
        plt.ylim(*y_lim)

    # 关键绘图逻辑：
    # 1. 绘制完整的真实值序列（训练部分 + 预测目标）
    train_len = len(known_y)
    all_y = np.concatenate([known_y, label_y])  # 拼接已知数据和真实目标
    x = np.arange(len(all_y))
    plt.plot(x, all_y, color='blue', marker='.')  # 蓝色线表示真实值

    # 2. 绘制预测值（只在预测区域显示）
    x = np.arange(train_len, len(all_y))  # 预测区域的x坐标
    
    if title == 'lorenz':
        # 对于lorenz数据，使用空心圆圈
        plt.scatter(x, predict_y, color='none', edgecolors='red', marker='o',
                    label='trained_loss:{:.2f},pcc:{:.2f}'.format(loss, pcc),
                    zorder=10, linewidths=1.2, s=40)
    else:
        # 对于其他数据，使用实心点并连接到已知数据
        plt.plot(x, predict_y, color='red', marker='.', 
                 label='trained_loss:{:.2f},pcc:{:.2f}'.format(loss, pcc))

        # 连接已知数据的最后一点和预测数据的第一点
        connected_y = np.stack([known_y[-1], predict_y[0]]) 
        x_connect = np.arange(train_len - 1, train_len + 1)
        plt.plot(x_connect, connected_y, color='red')

    # 3. 添加图例
    plt.legend(bbox_to_anchor=(0., 1.02, 1., .102), loc=0, ncol=2, mode="expand", borderaxespad=0.)

    # 4. 保存或显示
    if path is None:
        plt.show()
    else:
        plt.savefig(path)
        plt.clf()  # 清除当前图形

def demo_original_draw_pic():
    """演示原始draw_pic函数的使用"""
    print("🎨 演示原始draw_pic函数")
    
    # 生成示例数据（模拟Lorenz系统）
    np.random.seed(42)
    
    # 参数设置
    train_len = 40  # 训练长度
    predict_len = 18  # 预测长度（EMBEDDING_LEN - 1）
    
    # 生成时间序列
    t_total = np.linspace(0, 10, train_len + predict_len)
    
    # 模拟Lorenz系统的复杂动态
    true_signal = (10 * np.sin(t_total) * np.exp(-t_total/15) + 
                   5 * np.cos(2*t_total) * np.exp(-t_total/20) +
                   2 * np.sin(3*t_total))
    
    # 分割数据
    known_y = true_signal[:train_len]  # 训练时已知的数据
    label_y = true_signal[train_len:]  # 预测的真实目标
    
    # 模拟预测结果（添加一些误差）
    predict_y = label_y + np.random.normal(0, 0.3, len(label_y))
    
    # 计算性能指标
    loss = np.sqrt(np.mean((predict_y - label_y)**2))  # RMSE
    pcc = np.corrcoef(predict_y, label_y)[0, 1]  # 皮尔逊相关系数
    
    print(f"📊 数据统计:")
    print(f"   训练长度: {len(known_y)}")
    print(f"   预测长度: {len(label_y)}")
    print(f"   RMSE损失: {loss:.3f}")
    print(f"   PCC相关性: {pcc:.3f}")
    
    # 使用原始draw_pic函数绘制
    draw_pic(known_y, label_y, predict_y, loss, pcc,
             x_label='Time', y_label='Value', title='lorenz',
             path='original_draw_pic_demo.png', figsize=(8, 6))
    
    print(f"✅ 预测图已保存: original_draw_pic_demo.png")

def explain_eval_script_usage():
    """解释如何运行eval脚本生成预测图"""
    print("\n" + "="*60)
    print("📋 如何运行原始eval脚本生成预测图")
    print("="*60)
    
    print("""
🔧 运行步骤:

1. 确保环境配置:
   - 安装tensorflow: pip install tensorflow
   - 安装其他依赖: pip install numpy matplotlib scipy

2. 运行评估脚本:
   cd forecast/lorenz96
   python eval.py
   
   或者:
   cd forecast/lorenz_time_invariant  
   python eval.py

3. 图片保存位置:
   - 路径格式: logs/results/{config.name}/{set}/{idx}.png
   - 例如: logs/results/lorenz96_40_19_Yidx_0/train/0.png
   - config.name 根据配置自动生成

4. 图片内容:
   - 每个预测样本生成一张图
   - 蓝色线: 完整真实值序列
   - 红色圆圈: 模型预测值
   - 标题显示PCC相关系数
   - 图例显示RMSE损失和PCC

🎯 关键配置参数:

- MODEL_NAME: 'lorenz96' 或 'lorenz'
- TRAIN_LEN: 训练序列长度 (40)
- EMBEDDING_LEN: 嵌入长度 (19)
- Y_IDX: 目标变量索引 (0)

生成的config.name示例:
- lorenz96_40_19_Yidx_0
- lorenz_40_19_Yidx_0

📁 目录结构:
logs/
├── results/
│   ├── lorenz96_40_19_Yidx_0/
│   │   ├── train/
│   │   │   ├── 0.png
│   │   │   ├── 1.png
│   │   │   └── ...
│   │   └── val/
│   │       ├── 0.png
│   │       └── ...
│   └── lorenz_40_19_Yidx_0/
│       └── ...

🔍 图片特征:
- 字体: Times New Roman, 大小17
- 分辨率: 200 DPI
- 尺寸: (8, 6)
- 格式: PNG

💡 提示:
- 如果draw_pic调用被注释，需要取消注释
- 确保模型权重文件路径正确
- 第一次运行会自动创建results目录
""")

def main():
    """主函数"""
    print("🎨 原始模型draw_pic函数使用指南")
    print("="*50)
    
    # 演示原始函数
    demo_original_draw_pic()
    
    # 解释eval脚本用法
    explain_eval_script_usage()
    
    print("\n✅ 指南完成！")
    print("💡 您现在知道如何使用原始模型的绘图功能了。")

if __name__ == '__main__':
    main()
