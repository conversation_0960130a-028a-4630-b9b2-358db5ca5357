import sys
import os
# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import sys
import os
# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from forecast import st_delay_model
from data import data_processing
from forecast.lorenz96 import lorenz96_config
from scipy.stats import pearsonr, spearmanr
from sklearn.metrics import mean_absolute_error
import numpy as np
import pickle
import tensorflow as tf
import re
from utils import utils
import matplotlib
matplotlib.use('agg')
import matplotlib.pyplot as plt


def comprehensive_evaluate(y_true, y_pred, set_name=""):
    """综合评估函数"""
    y_true_flat = y_true.flatten()
    y_pred_flat = y_pred.flatten()

    # 基础指标
    rmse = np.sqrt(np.mean((y_pred_flat - y_true_flat) ** 2))
    mae = np.mean(np.abs(y_pred_flat - y_true_flat))
    mape = np.mean(np.abs((y_true_flat - y_pred_flat) / (y_true_flat + 1e-8))) * 100

    # 相关性指标
    pcc, pcc_p = pearsonr(y_pred_flat, y_true_flat)
    spearman_corr, spearman_p = spearmanr(y_pred_flat, y_true_flat)

    # 方向准确率
    direction_accuracy = calculate_direction_accuracy(y_true, y_pred)

    # 趋势一致性
    trend_consistency = calculate_trend_consistency(y_true, y_pred)

    # R²
    ss_res = np.sum((y_true_flat - y_pred_flat) ** 2)
    ss_tot = np.sum((y_true_flat - np.mean(y_true_flat)) ** 2)
    r2 = 1 - (ss_res / (ss_tot + 1e-8))

    results = {
        'RMSE': rmse,
        'MAE': mae,
        'MAPE': mape,
        'PCC': pcc,
        'PCC_P_Value': pcc_p,
        'Spearman_Corr': spearman_corr,
        'Spearman_P_Value': spearman_p,
        'Direction_Accuracy': direction_accuracy,
        'Trend_Consistency': trend_consistency,
        'R2': r2
    }

    print(f"\n{set_name} 集综合评估结果:")
    print("="*50)
    print(f"RMSE:              {rmse:.6f}")
    print(f"MAE:               {mae:.6f}")
    print(f"MAPE:              {mape:.2f}%")
    print(f"R²:                {r2:.6f}")
    print(f"Pearson相关系数:    {pcc:.6f} (p={pcc_p:.2e})")
    print(f"Spearman相关系数:   {spearman_corr:.6f} (p={spearman_p:.2e})")
    print(f"方向准确率:         {direction_accuracy:.6f}")
    print(f"趋势一致性:         {trend_consistency:.6f}")
    print("="*50)

    return results


def calculate_direction_accuracy(y_true, y_pred):
    """计算方向准确率"""
    if len(y_true.shape) > 1:
        # 对于多维数据，计算每个时间步的平均值
        y_true_mean = np.mean(y_true, axis=-1)
        y_pred_mean = np.mean(y_pred, axis=-1)
    else:
        y_true_mean = y_true
        y_pred_mean = y_pred

    # 计算变化方向
    true_diff = np.diff(y_true_mean)
    pred_diff = np.diff(y_pred_mean)

    # 方向一致性
    direction_match = np.sign(true_diff) == np.sign(pred_diff)
    return np.mean(direction_match)


def calculate_trend_consistency(y_true, y_pred):
    """计算趋势一致性"""
    if len(y_true.shape) > 1:
        y_true_mean = np.mean(y_true, axis=-1)
        y_pred_mean = np.mean(y_pred, axis=-1)
    else:
        y_true_mean = y_true
        y_pred_mean = y_pred

    # 使用线性回归斜率表示趋势
    x = np.arange(len(y_true_mean))
    true_slope = np.polyfit(x, y_true_mean, 1)[0]
    pred_slope = np.polyfit(x, y_pred_mean, 1)[0]

    # 趋势一致性 = 1 - |斜率差异| / (|真实斜率| + 小常数)
    consistency = 1 - np.abs(true_slope - pred_slope) / (np.abs(true_slope) + 1e-8)
    return max(0, consistency)  # 确保非负


def draw_pic(known_y, label_y, predict_y, loss, pcc, x_label=None, y_label=None, y_lim=None,
             title=None, path=None, figsize=None):
    plt.rcParams['figure.figsize'] = figsize 
    plt.rcParams['savefig.dpi'] = 200

    fontsize = 17
    plt.title(title + ", PCC:{:.2f}".format(pcc), fontdict={'family': 'Times New Roman', 'size': fontsize})
    plt.xlabel(x_label, fontdict={'family': 'Times New Roman', 'size': fontsize})
    plt.ylabel(y_label, fontdict={'family': 'Times New Roman', 'size': fontsize})
    plt.yticks(fontproperties='Times New Roman', size=fontsize)
    plt.xticks(fontproperties='Times New Roman', size=fontsize)

    if y_lim is not None:
        plt.ylim(*y_lim)

    train_len = len(known_y)
    all_y = np.concatenate([known_y, label_y])
    x = np.arange(len(all_y))
    plt.plot(x, all_y, color='blue', marker='.')

    x = np.arange(train_len, len(all_y))

    if title == 'lorenz':
        plt.scatter(x, predict_y, color='none', edgecolors='red', marker='o',
                    label='trained_loss:{:.2f},pcc:{:.2f}'.format(loss, pcc),
                    zorder=10, linewidths=1.2, s=40)
    else:
        plt.plot(x, predict_y, color='red', marker='.', label='trained_loss:{:.2f},pcc:{:.2f}'.format(loss, pcc))

        connected_y = np.stack([known_y[-1], predict_y[0]]) 
        x = np.arange(train_len - 1, train_len + 1)
        plt.plot(x, connected_y, color='red')

    plt.legend(bbox_to_anchor=(0., 1.02, 1., .102), loc=0, ncol=2, mode="expand", borderaxespad=0.)

    if path is None:
        plt.show()
    else:
        plt.savefig(path)
        plt.clf()


def get_model_path(base_log_dir, config):
    name_pattern = re.compile('.*\\({})'.format(re.escape(config.name)))
    # name_pattern = re.compile('.*noise_{:.1f}\)'.format(config.DATA_NOISE_STRENGTH))
    # name_pattern = re.compile('.*\(.*rate_{:.2f}\)'.format(config.DATASET_RATE))
    # name_pattern = re.compile('.*\(lorenz_{}.*\)'.format(config.TRAIN_LEN))

    file_pattern = re.compile('weights_epoch:{:0>4d}.*'.format(config.EPOCHS))
    model_path = None

    for d in os.listdir(base_log_dir):
        print(d)
        if name_pattern.match(d):
            for f in os.listdir(os.path.join(base_log_dir, d)):
                if file_pattern.match(f):
                    model_path = os.path.join(base_log_dir, d, f)
                    print('load weights from: {}'.format(model_path))
                    return model_path

    return model_path


if __name__ == '__main__':
    is_solar = False
    config = lorenz96_config.Lorenz96Config()
    config.BATCH_SIZE = 1 

    tf.keras.backend.clear_session() 

    gpus = tf.config.experimental.list_physical_devices(device_type='GPU')
    for gpu in gpus:
        tf.config.experimental.set_memory_growth(gpu, True)

    data = data_processing.load_lorenz96_data(N=config.K, F=config.F, time_range=(0, 3000), dt=0.02)

    train_idxs, val_idxs = data_processing.get_data_idxs_as_predict_idx_no_overlap(rate=1,
                                                                                   interval=60)

    mean_train_losses = []
    mean_train_pccs = []


    results = {}


    model = st_delay_model.STDelayModel(config, mode='evaluation', log_dir_suffix=config.name)

    noised_data, y = data_processing.add_noise(data, config) 
    train_generator = st_delay_model.DataGeneratorForLengthCmp(data, y, train_idxs, config)
    val_generator = None if val_idxs is None else st_delay_model.DataGeneratorForLengthCmp(data, y, val_idxs, config)

    # 指定具体的权重文件 - 使用新训练的模型
    model_path = r"C:\Users\<USER>\Desktop\重新Delay-Embedding-based-Forecast-Machine-master\logs\2025_08_01-09_06_27(lorenz96_40_19_Yidx_0)\weights_epoch_0150_loss_0.001.h5"

    print(model_path)
    # 使用原始的load_weights方法
    model.load_weights(model_path)

    sets = {'train': train_generator, 'val': val_generator}

    for set in sets:
        if sets[set] is None:
            continue
        print(set)
        generator = sets[set]
        idx = 0
        result_dir = '../../logs/results/{}/{}'.format(config.name, set)
        if not os.path.exists(result_dir):
            os.makedirs(result_dir)

        losses = []
        pccs = []

        predict_ys = []
        label_ys = []
        known_ys = []

        for item in generator.get_item():
            input_x, label_y, label_matrix = item[0][0], item[-1], item[1][0]

            predict_y_matrix = model.model.predict(input_x)[0]

            predict_y = utils.get_y_from_matrix(config, predict_y_matrix.T, weighted=False)

            known_y = input_x[0, :, config.Y_IDX] 
            label_y = item[-1][0]

            loss = np.sqrt(np.mean(np.square(label_y - predict_y)))
            known_ys.append(known_y)
            predict_ys.append(predict_y)
            label_ys.append(label_y)
            losses.append(loss)


            pcc, p_value = pearsonr(label_y, predict_y)
            pccs.append(pcc)

            # draw_pic(known_y, label_y, predict_y, loss, pcc=pcc, path=result_dir + '/{}.png'.format(idx),
            #          figsize=(8, 6), y_lim=None, x_label='Time', y_label='Value', title='lorenz') 

            idx += 1

            if idx % 100 == 0:
                print(idx)

        print(np.sum(np.array(losses) < 1.0))
        print('mean loss：', np.mean(losses))
        print('mean pcc:', np.mean(pccs))
        # with open(result_dir + '/losses.pkl', 'wb') as file:
        #     pickle.dump(np.array(losses, np.float32), file)
        print(np.argsort(losses)[:100])
        print(np.argsort(pccs)[::-1][:100])

        if set == 'train':
            prediction_results = {}
            prediction_results['train_ys'] = known_ys
            prediction_results['label_ys'] = label_ys
            prediction_results['predict_ys'] = predict_ys
            prediction_results['rmses'] = losses
            prediction_results['pccs'] = pccs
            with open('../../logs/results/{}_prediction_results.pkl'.format(config.name), 'wb') as file:
                pickle.dump(prediction_results, file)

