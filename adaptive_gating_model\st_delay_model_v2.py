#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自适应门控版本的ST-Delay模型
替换原始的简单concat融合为自适应交互门控
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import tensorflow as tf
from tensorflow.keras.layers import Input, SpatialDropout1D, TimeDistributed, Dense
from tensorflow.keras.models import Model
from tensorflow.keras.optimizers import Adam
import numpy as np
import datetime

# 导入原始模块
from forecast.st_delay_model import STDelayModel, DataGeneratorForLengthCmp
from utils.layers import Matrix2Y
from utils import layers

# 导入新的自适应门控模块
from adaptive_gating_model.adaptive_gating import AdaptiveGatingModule


class STDelayModelV2:
    """
    自适应门控版本的ST-Delay模型
    基于原始STDelayModel，只替换融合模块
    """

    def __init__(self, config, mode='training', log_dir_suffix='adaptive_gating_model'):
        # 修改log_dir_suffix以区分模型版本
        if log_dir_suffix == 'adaptive_gating_model':
            log_dir_suffix = f"{config.name}_adaptive_gating_model"

        self.config = config
        self.mode = mode
        self.log_dir_suffix = log_dir_suffix

        # 只在训练模式下设置日志目录
        if mode == 'training':
            self._setup_logging()
        else:
            self.log_dir = None

        # 构建模型
        self._build_model()
    
    def _build_model(self):
        """构建模型，使用自适应交互门控"""
        
        # 输入层
        input_layer = Input(shape=(self.config.TRAIN_LEN, self.config.K), name='input')
        
        # Dropout层
        input_drop = SpatialDropout1D(self.config.DROP_RATE, name='input_drop')(input_layer)
        
        # 空间模块（保持原始全连接）
        spatial_features = self._build_spatial_module(input_drop)
        
        # 时间模块（保持原始Transformer）
        temporal_features = self._build_temporal_module(input_drop)
        
        # 新的自适应交互门控融合
        adaptive_gating_module = AdaptiveGatingModule(self.config, name='adaptive_gating_module')
        merge_features = adaptive_gating_module([spatial_features, temporal_features])
        
        # 后续处理层（保持原始结构）
        delay_output = self._build_merge_layers(merge_features)
        
        # 输出层
        predict_y = Matrix2Y(self.config, name='predict_y')(delay_output)
        
        # 创建模型
        if self.mode == 'training':
            # 训练模式：包含损失层
            gt_y_matrix = Input(shape=(self.config.TRAIN_LEN, self.config.EMBEDDING_LEN),
                               name='gt_y_matrix', dtype=tf.float32)
            gt_y = Input(shape=(self.config.EMBEDDING_LEN - 1,), 
                        name='gt_y', dtype=tf.float32)
            
            # 损失层
            from utils.layers import KnownYLoss, TimeConsistentLoss, PredictYLoss
            known_y_loss = KnownYLoss(self.config, name='known_y_loss')([gt_y_matrix, delay_output])
            consistent_loss_layer = TimeConsistentLoss(self.config, name='consistent_loss')(delay_output)
            predict_y_loss = PredictYLoss(name='predict_y_loss')([gt_y, predict_y])
            
            self.model = Model(
                inputs=[input_layer, gt_y_matrix, gt_y], 
                outputs=[delay_output, predict_y, known_y_loss, consistent_loss_layer, predict_y_loss],
                name='st_delay_adaptive_gating_model'
            )
        else:
            # 评估模式
            self.model = Model(
                inputs=input_layer, 
                outputs=[delay_output, predict_y],
                name='st_delay_adaptive_gating_model'
            )
        
        print("自适应门控版ST-Delay模型构建完成")
        print(f"📊 模型参数统计:")
        self.model.summary()
    
    def _build_spatial_module(self, input_drop):
        """构建空间模块（保持原始结构）"""
        from forecast.st_delay_model import time_distributed_graph
        
        spatial_features = time_distributed_graph(
            input_drop, 
            self.config.SPATIAL_NODES,
            last_activation=self.config.MODULE_LAST_ACITVATION,
            last_bn=True, 
            activation=self.config.ACITVATION,
            name_prefix='spatial',
            kernel_initialzer=self.config.KERNEL_INITIALIZER,
            weight_decay=self.config.WEIGHT_DECAY, 
            bn=self.config.BN
        )
        
        return spatial_features
    
    def _build_temporal_module(self, input_drop):
        """构建时间模块（保持原始Transformer）"""
        # 位置编码
        positioned_input = layers.PositionEncodingLayer(
            self.config.TEMPORAL_DIM,
            self.config.TRAIN_LEN
        )(input_drop)

        # Transformer编码器层
        temporal_features = positioned_input
        for _ in range(self.config.ENCODING_LAYER_NUMS):
            temporal_features = layers.encoder_graph(
                self.config.TEMPORAL_DIM,
                self.config.NUM_HEADS,
                self.config.DIFF,
                self.config.TRAINING,
                rate=0,
                x=temporal_features
            )

        return temporal_features
    
    def _build_merge_layers(self, merge_features):
        """构建融合后的处理层"""
        from forecast.st_delay_model import time_distributed_graph
        
        delay_output = time_distributed_graph(
            merge_features, 
            self.config.MERGE_MAP_NODES, 
            last_activation=False,
            last_bn=False, 
            activation=self.config.ACITVATION,
            name_prefix='merge_delay',
            kernel_initialzer=self.config.KERNEL_INITIALIZER,
            weight_decay=self.config.WEIGHT_DECAY, 
            bn=self.config.BN
        )
        
        return delay_output
    
    def _setup_logging(self):
        """设置日志目录"""
        current_time = datetime.datetime.now().strftime("%Y_%m_%d-%H_%M_%S")
        self.log_dir = f"logs/{current_time}({self.log_dir_suffix})"
        
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
        
        print(f"日志目录: {self.log_dir}")

    def compile(self):
        """编译模型 - 使用与原始模型完全相同的方式"""
        print(self.model.outputs)

        if self.mode == 'training':
            # 使用add_loss方式，与原始模型保持一致
            optimizer = Adam(learning_rate=self.config.LR)

            losses = ['known_y_loss', 'consistent_loss']
            # 添加损失
            for loss_name in losses:
                layer = self.model.get_layer(loss_name)
                loss = layer.output * self.config.LOSS_WEIGHTS.get(loss_name, 1.)
                self.model.add_loss(loss)

            print(self.model.losses)

            # 编译模型 - 不指定外部损失函数，使用内部损失
            self.model.compile(
                optimizer=optimizer,
                run_eagerly=False
            )
        else:
            # 评估模式的编译
            self.model.compile(
                optimizer=Adam(learning_rate=self.config.LR),
                loss='mse'
            )

        print("模型编译完成")

    def train(self, train_generator, val_generator=None):
        """训练模型"""
        from tensorflow.keras.callbacks import ModelCheckpoint, EarlyStopping

        # 回调函数
        callbacks = []

        # 模型检查点
        checkpoint_path = f"{self.log_dir}/weights_epoch_{{epoch:04d}}_loss_{{val_loss:.3f}}.h5"
        checkpoint = ModelCheckpoint(
            checkpoint_path,
            monitor='val_loss',
            save_best_only=True,
            save_weights_only=True,
            verbose=1
        )
        callbacks.append(checkpoint)

        # 早停 - 增加耐心值，让复杂的自适应门控模型有更多训练时间
        early_stopping = EarlyStopping(
            monitor='val_loss',
            patience=25,  # 从10增加到25个epoch
            restore_best_weights=True,
            verbose=1
        )
        callbacks.append(early_stopping)

        # 开始训练
        history = self.model.fit(
            train_generator,
            validation_data=val_generator,
            epochs=self.config.EPOCHS,
            callbacks=callbacks,
            verbose=1
        )

        return history

    def load_weights(self, weights_path):
        """加载权重 - 直接使用h5py手动加载"""
        print(f"尝试加载权重: {weights_path}")

        import h5py
        import numpy as np

        try:
            # 直接使用h5py手动加载权重，绕过TensorFlow的版本检查
            with h5py.File(weights_path, 'r') as f:
                # 获取所有层的名称
                if 'layer_names' in f.attrs:
                    layer_names = []
                    for name in f.attrs['layer_names']:
                        if hasattr(name, 'decode'):
                            layer_names.append(name.decode('utf8'))
                        else:
                            layer_names.append(str(name))

                    # 为每一层加载权重
                    for layer in self.model.layers:
                        if layer.name in layer_names and layer.name in f:
                            g = f[layer.name]
                            if 'weight_names' in g.attrs:
                                weight_names = []
                                for name in g.attrs['weight_names']:
                                    if hasattr(name, 'decode'):
                                        weight_names.append(name.decode('utf8'))
                                    else:
                                        weight_names.append(str(name))

                                # 加载权重数据
                                weight_values = []
                                for weight_name in weight_names:
                                    if weight_name in g:
                                        weight_values.append(np.array(g[weight_name]))

                                # 设置权重
                                if weight_values and len(weight_values) == len(layer.get_weights()):
                                    layer.set_weights(weight_values)
                                    print(f"已加载层 {layer.name} 的权重")

            print(f"权重加载成功: {weights_path}")

        except Exception as e:
            print(f"手动权重加载失败: {e}")
            print("尝试使用TensorFlow标准方法...")

            # 如果手动加载失败，尝试标准方法
            try:
                self.model.load_weights(weights_path)
                print(f"标准方法权重加载成功: {weights_path}")
            except Exception as e2:
                print(f"标准方法也失败: {e2}")
                raise e2


def create_adaptive_gating_model(config, mode='training'):
    """
    创建自适应门控版本模型的工厂函数
    
    Args:
        config: 配置对象
        mode: 模式 ('training' 或 'evaluation')
        
    Returns:
        STDelayModelV2实例
    """
    return STDelayModelV2(config, mode=mode)


if __name__ == "__main__":
    # 测试代码
    from forecast.lorenz96.lorenz96_config import Lorenz96Config
    
    print("🔧 测试自适应门控版ST-Delay模型...")
    
    config = Lorenz96Config()
    
    # 创建模型
    model = create_adaptive_gating_model(config, mode='training')
    
    # 编译模型
    model.compile()
    
    print("✅ 自适应门控版模型创建和编译成功！")
    
    # 显示模型结构
    print("\n📋 模型结构:")
    model.model.summary()
    
    # 参数对比
    from adaptive_gating_model.adaptive_gating import count_adaptive_gating_parameters
    param_stats = count_adaptive_gating_parameters(config)
    
    print(f"\n 参数对比:")
    print(f"自适应门控模块参数: {param_stats['total_params']:,}")
    print(f"相对简单concat增加: {param_stats['parameter_increase']:,}")
    print(f"主要参数分布:")
    print(f"  - 特征变换: {param_stats['transform_params']:,}")
    print(f"  - 交互网络: {param_stats['interaction_params']:,}")
    print(f"  - 门控网络: {param_stats['gate_params']:,}")
    print(f"  - 融合网络: {param_stats['fusion_params']:,}")
