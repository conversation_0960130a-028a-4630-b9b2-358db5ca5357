#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TCN + 自适应门控双重替换模型评估脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
from scipy.stats import pearsonr
from data import data_processing
from forecast.lorenz96 import lorenz96_config
from adaptive_gating_model.st_delay_model_v2 import STDelayModelV2


def eval_model(model, data_generator, config, set_name):
    """评估模型性能"""
    losses = []
    pccs = []
    idxs = []

    for i in range(len(data_generator)):
        batch_data = data_generator[i]
        
        if len(batch_data) == 2:
            x, y = batch_data
        else:
            x, y = batch_data[0], batch_data[-1]
        
        # 预测
        predictions = model.model.predict(x, verbose=0)
        predict_y = predictions[1] if len(predictions) > 1 else predictions[0]

        # 计算RMSE
        loss = np.sqrt(np.mean((predict_y - y) ** 2))
        
        # 计算PCC
        pcc = pearsonr(predict_y.flatten(), y.flatten())[0]
        
        losses.append(loss)
        pccs.append(pcc)
        idxs.append(data_generator.idxs[i])
    
    # 统计结果
    mean_loss = np.mean(losses)
    mean_pcc = np.mean(pccs)
    
    print(f"{len(losses)}")
    print(f'mean loss: {mean_loss}')
    print(f'mean pcc: {mean_pcc}')
    
    return losses, pccs, idxs


def main():
    """主评估函数"""
    print("🔍 开始评估TCN+自适应门控双重替换模型...")
    
    # 配置
    config = lorenz96_config.Lorenz96Config()
    
    # 🔥 关键配置：启用TCN时间模块
    config.USE_TCN_TEMPORAL = True
    config.TCN_FILTERS = 64
    
    print("📊 加载数据...")
    data = data_processing.load_lorenz96_data(N=config.K, F=config.F, time_range=(0, 3000), dt=0.02)

    # 数据分割
    train_idxs, val_idxs = data_processing.get_data_idxs_as_predict_idx_no_overlap(rate=1, interval=60)

    # 创建模型（评估模式）
    print("🏗️ 创建TCN+自适应门控模型...")
    model = STDelayModelV2(config, mode='prediction')

    # 添加噪声
    noised_data, y = data_processing.add_noise(data, config) 
    
    # 使用原始的数据生成器
    from forecast import st_delay_model
    train_generator = st_delay_model.DataGeneratorForLengthCmp(data, y, train_idxs, config)
    val_generator = None if val_idxs is None else st_delay_model.DataGeneratorForLengthCmp(data, y, val_idxs, config)

    # 🔥 指定TCN+自适应门控权重文件路径（需要训练完成后更新）
    model_path = r"C:\Users\<USER>\Desktop\重新Delay-Embedding-based-Forecast-Machine-master\logs\2025_07_31-XX_XX_XX(lorenz96_40_19_Yidx_0_tcn_adaptive_gating_model)\weights_epoch_XXXX_loss_X.XXX.h5"
    
    print("⚠️  请先运行训练脚本，然后更新此路径！")
    print(f"📁 当前路径: {model_path}")
    
    # 暂时注释掉加载权重，等训练完成后再启用
    # model.load_weights(model_path)

    # 评估集合
    sets = {'train': train_generator, 'val': val_generator}

    print("\n📈 开始评估...")
    for set_name, data_generator in sets.items():
        if data_generator is not None:
            print(f"\n🔍 评估 {set_name} 集:")
            losses, pccs, idxs = eval_model(model, data_generator, config, set_name)


if __name__ == '__main__':
    try:
        main()
        print("\n✅ TCN+自适应门控模型评估完成！")
        
    except Exception as e:
        print(f"\n❌ 评估过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
