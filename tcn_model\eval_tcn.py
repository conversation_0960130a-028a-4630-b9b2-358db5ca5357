import sys
import os
# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import sys
import os
# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tcn_model.st_delay_model_v2 import STDelayModelV2
from data import data_processing
from forecast.lorenz96 import lorenz96_config
from scipy.stats import pearsonr, spearmanr
import numpy as np
import pickle
import tensorflow as tf
import re
from utils import utils
import matplotlib
matplotlib.use('agg')
import matplotlib.pyplot as plt


def draw_pic(known_y, label_y, predict_y, loss, pcc, x_label=None, y_label=None, y_lim=None,
             title=None, path=None, figsize=None):
    plt.rcParams['figure.figsize'] = figsize 
    plt.rcParams['savefig.dpi'] = 200

    fontsize = 17
    plt.title(title + ", PCC:{:.2f}".format(pcc), fontdict={'family': 'Times New Roman', 'size': fontsize})
    plt.xlabel(x_label, fontdict={'family': 'Times New Roman', 'size': fontsize})
    plt.ylabel(y_label, fontdict={'family': 'Times New Roman', 'size': fontsize})
    plt.yticks(fontproperties='Times New Roman', size=fontsize)
    plt.xticks(fontproperties='Times New Roman', size=fontsize)

    if y_lim is not None:
        plt.ylim(*y_lim)

    train_len = len(known_y)
    all_y = np.concatenate([known_y, label_y])
    x = np.arange(len(all_y))
    plt.plot(x, all_y, color='blue', marker='.')

    x = np.arange(train_len, len(all_y))

    if title == 'lorenz':
        plt.scatter(x, predict_y, color='none', edgecolors='red', marker='o',
                    label='trained_loss:{:.2f},pcc:{:.2f}'.format(loss, pcc),
                    zorder=10, linewidths=1.2, s=40)
    else:
        plt.plot(x, predict_y, color='red', marker='o', markersize=3,
                 label='trained_loss:{:.2f},pcc:{:.2f}'.format(loss, pcc))

    plt.legend(prop={'family': 'Times New Roman', 'size': fontsize})
    plt.tight_layout()
    plt.savefig(path)
    plt.close()


def eval_model(model, data_generator, config, set_name):
    print(set_name)
    losses = []
    maes = []
    pccs = []
    spearman_corrs = []
    idxs = []
    for i, batch_data in enumerate(data_generator):
        if i % 100 == 0:
            print(i)
        # 处理数据生成器返回的格式
        if len(batch_data) == 3:
            # 训练模式: [x, y_matrix, y]
            x, y_matrix, y = batch_data
        else:
            # 其他格式
            x, y = batch_data[0], batch_data[-1]

        delay_output, predict_y = model.model.predict(x, verbose=0)
        loss = np.sqrt(np.mean((predict_y - y) ** 2))  # RMSE
        mae = np.mean(np.abs(predict_y - y))  # MAE
        pcc = pearsonr(predict_y.flatten(), y.flatten())[0]
        spearman_corr = spearmanr(predict_y.flatten(), y.flatten())[0]

        losses.append(loss)
        maes.append(mae)
        pccs.append(pcc)
        spearman_corrs.append(spearman_corr)
        idxs.append(data_generator.idxs[i])
    print(len(losses))
    print('mean RMSE:', np.mean(losses))
    print('mean MAE:', np.mean(maes))
    print('mean PCC:', np.mean(pccs))
    print('mean Spearman:', np.mean(spearman_corrs))
    print(np.array(idxs))
    print(np.argsort(pccs))
    return losses, maes, pccs, spearman_corrs, idxs


if __name__ == '__main__':

    config = lorenz96_config.Lorenz96Config()
    # 启用TCN
    config.USE_TCN_TEMPORAL = True

    print('loading lorenz  data...')
    data = data_processing.load_lorenz96_data(N=config.K, F=config.F, time_range=(0, 3000), dt=0.02)

    print(len(data))

    # 创建TCN模型
    model = STDelayModelV2(config, mode='prediction')

    train_idxs, val_idxs = data_processing.get_data_idxs_as_predict_idx_no_overlap(rate=1,
                                                                                   interval=60)

    mean_train_losses = []
    mean_train_pccs = []

    results = {}

    noised_data, y = data_processing.add_noise(data, config) 
    # 使用原始的数据生成器
    from forecast import st_delay_model
    train_generator = st_delay_model.DataGeneratorForLengthCmp(data, y, train_idxs, config)
    val_generator = None if val_idxs is None else st_delay_model.DataGeneratorForLengthCmp(data, y, val_idxs, config)

    # 指定TCN权重文件路径（使用刚训练完成的TCN模型）
    model_path = r"C:\Users\<USER>\Desktop\重新Delay-Embedding-based-Forecast-Machine-master\logs\2025_07_29-16_27_18(lorenz96_40_19_Yidx_0_tcn_model_2)\weights_epoch_0085_loss_0.001.h5"

    print(model_path)
    # 使用原始的load_weights方法
    model.load_weights(model_path)

    sets = {'train': train_generator, 'val': val_generator}

    for set_name, data_generator in sets.items():
        if data_generator is None:
            continue
        losses, maes, pccs, spearman_corrs, idxs = eval_model(model, data_generator, config, set_name)
        results[set_name] = {'losses': losses, 'maes': maes, 'pccs': pccs, 'spearman_corrs': spearman_corrs, 'idxs': idxs}

    # 保存结果
    with open('tcn_eval_results.pkl', 'wb') as f:
        pickle.dump(results, f)

    print("TCN模型评估完成，结果已保存到 tcn_eval_results.pkl")
