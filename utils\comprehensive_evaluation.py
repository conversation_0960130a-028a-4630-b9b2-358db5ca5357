#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合评估指标模块
提供多种评估指标来全面衡量时间序列预测模型的性能
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy import stats
from scipy.spatial.distance import j<PERSON><PERSON><PERSON><PERSON>
from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error
import seaborn as sns


class ComprehensiveEvaluator:
    """综合评估器"""
    
    def __init__(self):
        self.metrics = {}
        
    def evaluate_all(self, y_true, y_pred, verbose=True):
        """计算所有评估指标"""
        
        # 基础回归指标
        self.metrics.update(self._basic_regression_metrics(y_true, y_pred))
        
        # 相关性指标
        self.metrics.update(self._correlation_metrics(y_true, y_pred))
        
        # 时间序列专用指标
        self.metrics.update(self._time_series_metrics(y_true, y_pred))
        
        # 分布相似性指标
        self.metrics.update(self._distribution_metrics(y_true, y_pred))
        
        if verbose:
            self.print_results()
            
        return self.metrics
    
    def _basic_regression_metrics(self, y_true, y_pred):
        """基础回归指标"""
        y_true_flat = y_true.flatten()
        y_pred_flat = y_pred.flatten()
        
        metrics = {
            'RMSE': np.sqrt(mean_squared_error(y_true_flat, y_pred_flat)),
            'MAE': mean_absolute_error(y_true_flat, y_pred_flat),
            'MSE': mean_squared_error(y_true_flat, y_pred_flat),
            'MAPE': np.mean(np.abs((y_true_flat - y_pred_flat) / (y_true_flat + 1e-8))) * 100,
            'R2': r2_score(y_true_flat, y_pred_flat)
        }
        
        return metrics
    
    def _correlation_metrics(self, y_true, y_pred):
        """相关性指标"""
        y_true_flat = y_true.flatten()
        y_pred_flat = y_pred.flatten()
        
        # 皮尔逊相关系数
        pearson_corr, pearson_p = stats.pearsonr(y_true_flat, y_pred_flat)
        
        # 斯皮尔曼相关系数
        spearman_corr, spearman_p = stats.spearmanr(y_true_flat, y_pred_flat)
        
        # 肯德尔相关系数
        kendall_corr, kendall_p = stats.kendalltau(y_true_flat, y_pred_flat)
        
        metrics = {
            'Pearson_Corr': pearson_corr,
            'Pearson_P_Value': pearson_p,
            'Spearman_Corr': spearman_corr,
            'Spearman_P_Value': spearman_p,
            'Kendall_Corr': kendall_corr,
            'Kendall_P_Value': kendall_p
        }
        
        return metrics
    
    def _time_series_metrics(self, y_true, y_pred):
        """时间序列专用指标"""
        
        # 方向准确率
        direction_accuracy = self._direction_accuracy(y_true, y_pred)
        
        # 趋势一致性
        trend_consistency = self._trend_consistency(y_true, y_pred)
        
        metrics = {
            'Direction_Accuracy': direction_accuracy,
            'Trend_Consistency': trend_consistency
        }
        
        return metrics
    
    def _distribution_metrics(self, y_true, y_pred):
        """分布相似性指标"""
        y_true_flat = y_true.flatten()
        y_pred_flat = y_pred.flatten()
        
        # KL散度 (需要将数据转换为概率分布)
        kl_div = self._kl_divergence(y_true_flat, y_pred_flat)
        
        # JS散度
        js_div = self._js_divergence(y_true_flat, y_pred_flat)
        
        # Wasserstein距离
        wasserstein_dist = stats.wasserstein_distance(y_true_flat, y_pred_flat)
        
        metrics = {
            'KL_Divergence': kl_div,
            'JS_Divergence': js_div,
            'Wasserstein_Distance': wasserstein_dist
        }
        
        return metrics
    
    def _direction_accuracy(self, y_true, y_pred):
        """计算方向准确率"""
        if len(y_true.shape) > 1:
            # 对于多维数据，计算每个时间步的平均值
            y_true_mean = np.mean(y_true, axis=-1)
            y_pred_mean = np.mean(y_pred, axis=-1)
        else:
            y_true_mean = y_true
            y_pred_mean = y_pred
            
        # 计算变化方向
        true_diff = np.diff(y_true_mean)
        pred_diff = np.diff(y_pred_mean)
        
        # 方向一致性
        direction_match = np.sign(true_diff) == np.sign(pred_diff)
        return np.mean(direction_match)
    
    def _trend_consistency(self, y_true, y_pred):
        """计算趋势一致性"""
        if len(y_true.shape) > 1:
            y_true_mean = np.mean(y_true, axis=-1)
            y_pred_mean = np.mean(y_pred, axis=-1)
        else:
            y_true_mean = y_true
            y_pred_mean = y_pred
            
        # 使用线性回归斜率表示趋势
        x = np.arange(len(y_true_mean))
        true_slope = np.polyfit(x, y_true_mean, 1)[0]
        pred_slope = np.polyfit(x, y_pred_mean, 1)[0]
        
        # 趋势一致性 = 1 - |斜率差异| / (|真实斜率| + 小常数)
        consistency = 1 - np.abs(true_slope - pred_slope) / (np.abs(true_slope) + 1e-8)
        return max(0, consistency)  # 确保非负
    
    def _kl_divergence(self, y_true, y_pred):
        """计算KL散度"""
        # 将数据转换为概率分布
        bins = 50
        hist_true, bin_edges = np.histogram(y_true, bins=bins, density=True)
        hist_pred, _ = np.histogram(y_pred, bins=bin_edges, density=True)
        
        # 添加小常数避免log(0)
        hist_true = hist_true + 1e-10
        hist_pred = hist_pred + 1e-10
        
        # 归一化
        hist_true = hist_true / np.sum(hist_true)
        hist_pred = hist_pred / np.sum(hist_pred)
        
        # 计算KL散度
        kl_div = np.sum(hist_true * np.log(hist_true / hist_pred))
        return kl_div
    
    def _js_divergence(self, y_true, y_pred):
        """计算JS散度"""
        bins = 50
        hist_true, bin_edges = np.histogram(y_true, bins=bins, density=True)
        hist_pred, _ = np.histogram(y_pred, bins=bin_edges, density=True)
        
        # 归一化
        hist_true = hist_true / np.sum(hist_true)
        hist_pred = hist_pred / np.sum(hist_pred)
        
        # 计算JS散度
        js_div = jensenshannon(hist_true, hist_pred)
        return js_div
    
    def print_results(self):
        """打印评估结果"""
        print("\n" + "="*60)
        print("                 综合评估结果")
        print("="*60)
        
        print("\n【基础回归指标】")
        print(f"  RMSE:                {self.metrics['RMSE']:.6f}")
        print(f"  MAE:                 {self.metrics['MAE']:.6f}")
        print(f"  MSE:                 {self.metrics['MSE']:.6f}")
        print(f"  MAPE:                {self.metrics['MAPE']:.2f}%")
        print(f"  R²:                  {self.metrics['R2']:.6f}")
        
        print("\n【相关性指标】")
        print(f"  Pearson相关系数:      {self.metrics['Pearson_Corr']:.6f}")
        print(f"  Spearman相关系数:     {self.metrics['Spearman_Corr']:.6f}")
        print(f"  Kendall相关系数:      {self.metrics['Kendall_Corr']:.6f}")
        
        print("\n【时间序列指标】")
        print(f"  方向准确率:           {self.metrics['Direction_Accuracy']:.6f}")
        print(f"  趋势一致性:           {self.metrics['Trend_Consistency']:.6f}")
        
        print("\n【分布相似性指标】")
        print(f"  KL散度:              {self.metrics['KL_Divergence']:.6f}")
        print(f"  JS散度:              {self.metrics['JS_Divergence']:.6f}")
        print(f"  Wasserstein距离:     {self.metrics['Wasserstein_Distance']:.6f}")
        
        print("="*60)
    
    def plot_comprehensive_analysis(self, y_true, y_pred, save_path=None):
        """绘制综合分析图"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('综合评估分析', fontsize=16)
        
        y_true_flat = y_true.flatten()
        y_pred_flat = y_pred.flatten()
        
        # 1. 预测vs真实值散点图
        axes[0, 0].scatter(y_true_flat, y_pred_flat, alpha=0.5)
        axes[0, 0].plot([y_true_flat.min(), y_true_flat.max()], 
                       [y_true_flat.min(), y_true_flat.max()], 'r--')
        axes[0, 0].set_xlabel('真实值')
        axes[0, 0].set_ylabel('预测值')
        axes[0, 0].set_title('预测vs真实值')
        
        # 2. 残差分布
        residuals = y_true_flat - y_pred_flat
        axes[0, 1].hist(residuals, bins=50, alpha=0.7)
        axes[0, 1].set_xlabel('残差')
        axes[0, 1].set_ylabel('频次')
        axes[0, 1].set_title('残差分布')
        
        # 3. 时间序列对比（取前100个点）
        n_points = min(100, len(y_true_flat))
        axes[0, 2].plot(y_true_flat[:n_points], label='真实值', alpha=0.8)
        axes[0, 2].plot(y_pred_flat[:n_points], label='预测值', alpha=0.8)
        axes[0, 2].set_xlabel('时间步')
        axes[0, 2].set_ylabel('值')
        axes[0, 2].set_title('时间序列对比')
        axes[0, 2].legend()
        
        # 4. Q-Q图
        stats.probplot(residuals, dist="norm", plot=axes[1, 0])
        axes[1, 0].set_title('残差Q-Q图')
        
        # 5. 残差vs预测值
        axes[1, 1].scatter(y_pred_flat, residuals, alpha=0.5)
        axes[1, 1].axhline(y=0, color='r', linestyle='--')
        axes[1, 1].set_xlabel('预测值')
        axes[1, 1].set_ylabel('残差')
        axes[1, 1].set_title('残差vs预测值')
        
        # 6. 指标雷达图
        self._plot_radar_chart(axes[1, 2])
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def _plot_radar_chart(self, ax):
        """绘制指标雷达图"""
        # 选择关键指标
        metrics_for_radar = {
            'R²': self.metrics['R2'],
            'Pearson相关': self.metrics['Pearson_Corr'],
            '方向准确率': self.metrics['Direction_Accuracy'],
            '趋势一致性': self.metrics['Trend_Consistency'],
            '归一化RMSE': 1 - min(1, self.metrics['RMSE']),  # 归一化RMSE
        }
        
        angles = np.linspace(0, 2*np.pi, len(metrics_for_radar), endpoint=False)
        values = list(metrics_for_radar.values())
        
        # 闭合雷达图
        angles = np.concatenate((angles, [angles[0]]))
        values = values + [values[0]]
        
        ax.plot(angles, values, 'o-', linewidth=2)
        ax.fill(angles, values, alpha=0.25)
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(metrics_for_radar.keys())
        ax.set_ylim(0, 1)
        ax.set_title('关键指标雷达图')
        ax.grid(True)


def quick_evaluate(y_true, y_pred, plot=True, save_path=None):
    """快速评估函数"""
    evaluator = ComprehensiveEvaluator()
    metrics = evaluator.evaluate_all(y_true, y_pred)
    
    if plot:
        evaluator.plot_comprehensive_analysis(y_true, y_pred, save_path)
    
    return metrics


if __name__ == "__main__":
    # 示例用法
    np.random.seed(42)
    y_true = np.random.randn(100, 10)
    y_pred = y_true + np.random.randn(100, 10) * 0.1
    
    metrics = quick_evaluate(y_true, y_pred)
