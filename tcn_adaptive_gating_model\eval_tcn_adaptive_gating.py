#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TCN + 自适应门控混合模型评估脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
from scipy.stats import pearsonr
from data import data_processing
from forecast.lorenz96 import lorenz96_config

# 导入TCN+自适应门控模型
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from tcn_adaptive_gating_model import TCNAdaptiveGatingModel


def eval_model(model, data_generator, config, set_name):
    """评估模型性能"""
    losses = []
    pccs = []
    idxs = []

    print(f" 评估 {set_name} 集...")
    for i in range(len(data_generator)):
        if i % 50 == 0:
            print(f"   处理样本: {i}/{len(data_generator)}")
            
        batch_data = data_generator[i]
        
        if len(batch_data) == 2:
            x, y = batch_data
        else:
            x, y = batch_data[0], batch_data[-1]
        
        # 预测
        predictions = model.model.predict(x, verbose=0)
        predict_y = predictions[1] if len(predictions) > 1 else predictions[0]

        # 计算RMSE
        loss = np.sqrt(np.mean((predict_y - y) ** 2))
        
        # 计算PCC
        pcc = pearsonr(predict_y.flatten(), y.flatten())[0]
        
        losses.append(loss)
        pccs.append(pcc)
        idxs.append(data_generator.idxs[i])
    
    # 统计结果
    mean_loss = np.mean(losses)
    mean_pcc = np.mean(pccs)
    
    print(f" {set_name} 集结果:")
    print(f"   样本数量: {len(losses)}")
    print(f"   平均RMSE: {mean_loss:.6f}")
    print(f"   平均PCC: {mean_pcc:.6f}")
    
    return losses, pccs, idxs


def main():
    """主评估函数"""
    print(" 开始评估TCN+自适应门控混合模型")
    print("=" * 60)
    print(" 模型特性:")
    print("    时间模块: TCN (时间卷积网络)")
    print("    融合模块: 自适应门控机制")
    print("    空间模块: 全连接网络")
    print("=" * 60)
    
    # 配置
    config = lorenz96_config.Lorenz96Config()
    
    # TCN相关配置
    config.USE_TCN_TEMPORAL = True
    config.TCN_FILTERS = 64
    
    print(" 加载数据...")
    data = data_processing.load_lorenz96_data(N=config.K, F=config.F, time_range=(0, 3000), dt=0.02)

    # 数据分割
    train_idxs, val_idxs = data_processing.get_data_idxs_as_predict_idx_no_overlap(rate=1, interval=60)

    # 创建模型（评估模式）
    print(" 创建TCN+自适应门控混合模型...")
    model = TCNAdaptiveGatingModel(config, mode='prediction')

    # 添加噪声
    noised_data, y = data_processing.add_noise(data, config) 
    
    # 使用原始的数据生成器
    from forecast import st_delay_model
    train_generator = st_delay_model.DataGeneratorForLengthCmp(data, y, train_idxs, config)
    val_generator = None if val_idxs is None else st_delay_model.DataGeneratorForLengthCmp(data, y, val_idxs, config)

    #  指定TCN+自适应门控权重文件路径
    # 注意：需要训练完成后更新此路径
    model_path = r"C:\Users\<USER>\Desktop\重新Delay-Embedding-based-Forecast-Machine-master\logs\PLACEHOLDER_PATH\weights_epoch_XXXX_loss_X.XXX.h5"
    
    print("  请先运行训练脚本，然后更新权重文件路径！")
    print(f" 当前路径: {model_path}")
    
    # 检查权重文件是否存在
    if os.path.exists(model_path):
        print(" 加载权重...")
        model.load_weights(model_path)
        
        # 评估集合
        sets = {'train': train_generator, 'val': val_generator}

        print("\n 开始评估...")
        results = {}
        for set_name, data_generator in sets.items():
            if data_generator is not None:
                losses, pccs, idxs = eval_model(model, data_generator, config, set_name)
                results[set_name] = {
                    'losses': losses,
                    'pccs': pccs,
                    'idxs': idxs,
                    'mean_loss': np.mean(losses),
                    'mean_pcc': np.mean(pccs)
                }
        
        # 总结结果
        print("\n 评估总结:")
        print("=" * 60)
        for set_name, result in results.items():
            print(f"{set_name.upper()} 集:")
            print(f"   RMSE: {result['mean_loss']:.6f}")
            print(f"   PCC:  {result['mean_pcc']:.6f}")
        print("=" * 60)
        
        return results
    else:
        print(" 权重文件不存在，请先训练模型！")
        return None


if __name__ == '__main__':
    try:
        results = main()
        if results:
            print("\n TCN+自适应门控混合模型评估完成！")
        else:
            print("\n 评估未完成，请检查权重文件路径")
        
    except Exception as e:
        print(f"\n 评估过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
