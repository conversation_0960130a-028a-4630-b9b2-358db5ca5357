#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TCN + 自适应门控混合模型训练脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import tensorflow as tf
from data import data_processing
from forecast.lorenz96 import lorenz96_config
from forecast import st_delay_model

# 导入TCN+自适应门控模型
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from tcn_adaptive_gating_model import TCNAdaptiveGatingModel


def train_tcn_adaptive_gating_model():
    """训练TCN+自适应门控混合模型"""

    print(" 开始训练TCN+自适应门控混合模型")
    print("=" * 60)
    print(" 模型特性:")
    print("    时间模块: TCN (时间卷积网络)")
    print("    融合模块: 自适应门控机制")
    print("    空间模块: 全连接网络")
    print("=" * 60)
    
    # 配置
    config = lorenz96_config.Lorenz96Config()
    
    # TCN相关配置
    config.USE_TCN_TEMPORAL = True
    config.TCN_FILTERS = 64
    
    # GPU配置
    gpus = tf.config.experimental.list_physical_devices(device_type='GPU')
    for gpu in gpus:
        tf.config.experimental.set_memory_growth(gpu, True)
    
    # 加载数据
    print(" 加载Lorenz96数据...")
    data = data_processing.load_lorenz96_data(N=config.K, F=config.F, time_range=(0, 3000), dt=0.02)

    # 数据分割
    train_idxs, val_idxs = data_processing.get_data_idxs_as_predict_idx_no_overlap(rate=0.9, interval=60)
    print(f" 训练样本数: {len(train_idxs)}")
    print(f" 验证样本数: {len(val_idxs) if val_idxs is not None else 0}")

    # 添加噪声
    data, y = data_processing.add_noise(data, config)

    # 创建数据生成器
    train_generator = st_delay_model.DataGeneratorForLengthCmp(data, y, train_idxs, config)
    val_generator = None if val_idxs is None else st_delay_model.DataGeneratorForLengthCmp(data, y, val_idxs, config)

    # 创建TCN+自适应门控混合模型
    print("\n 创建TCN+自适应门控混合模型...")
    model = TCNAdaptiveGatingModel(config, mode='training', log_dir_suffix='tcn_adaptive_gating')

    # 编译模型
    print("\n 编译模型...")
    model.compile()

    # 显示模型信息
    print("\n 模型结构:")
    model.model.summary()

    # 参数统计
    try:
        from adaptive_gating_model.adaptive_gating import count_adaptive_gating_parameters
        param_stats = count_adaptive_gating_parameters(config)

        print(f"\n 自适应门控参数统计:")
        print(f" 总参数量: {param_stats['total_params']:,}")
        print(f" 复杂度级别: {param_stats['complexity_level']}")
        print(f" 主要组件:")
        print(f"   - 深层特征变换: {param_stats['transform_params']:,}")
        print(f"   - 多层交互网络: {param_stats['interaction_params']:,}")
        print(f"   - 复杂门控网络: {param_stats['gate_params']:,}")
        print(f"   - 动态路由网络: {param_stats['routing_params']:,}")
    except Exception as e:
        print(f" 参数统计失败: {e}")

    # 开始训练
    print("\n 开始训练...")
    print("=" * 60)
    model.train(train_generator, val_generator)

    print("\n TCN+自适应门控混合模型训练完成！")
    print(f" 模型保存在: {model.log_dir}")
    
    return model


if __name__ == '__main__':
    try:
        model = train_tcn_adaptive_gating_model()
        print("\n 训练成功完成！")

    except Exception as e:
        print(f"\n 训练过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
